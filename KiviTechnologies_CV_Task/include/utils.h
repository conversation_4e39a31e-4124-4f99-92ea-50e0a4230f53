#ifndef UTILS_H
#define UTILS_H

#include <opencv2/opencv.hpp>
#include <vector>
#include <string>
#include <fstream>
#include <sstream>
#include <iostream>
#include <limits>

// CSV reading function
std::vector<std::vector<double>> readCSV(const std::string& filename);

// PLY file operations
std::vector<std::vector<std::vector<float>>> readPLYFileWithNormals(const std::string& filename, int width, int height);
void writePLYFile(const std::string& filename, const std::vector<std::vector<std::vector<float>>>& organized_point_cloud, bool organized = false);

// Task 1: Double Three-step Phase-shifting Algorithm
namespace DoubleThreeStep {
    // Generate sinusoidal patterns
    std::vector<cv::Mat> generateSinusoidalPatterns(int width = 1280, int height = 720, int period = 10);
    
    // Compute phase maps using Eq. 7
    cv::Mat computePhaseMap(const std::vector<cv::Mat>& patterns);
    
    // Average phase maps
    cv::Mat averagePhaseMaps(const cv::Mat& phaseMap1, const cv::Mat& phaseMap2);
    
    // Phase unwrapping using gray codes
    cv::Mat unwrapPhase(const cv::Mat& wrappedPhase, const std::vector<cv::Mat>& grayCodes);
    
    // Load gray code patterns
    std::vector<cv::Mat> loadGrayCodePatterns(const std::string& basePath);
    
    // Main function for Task 1
    void executeTask1();
}

// Task 2: 3D Bilateral Filter
namespace BilateralFilter3D {
    // Apply 3D bilateral filter to organized point cloud
    std::vector<std::vector<std::vector<float>>> applyBilateralFilter(
        const std::vector<std::vector<std::vector<float>>>& pointCloud,
        double spatialSigma, double normalSigma);
    
    // Helper functions
    double computeDistance(const std::vector<float>& p1, const std::vector<float>& p2);
    double computeNormalDistance(const std::vector<float>& n1, const std::vector<float>& n2);
    bool isValidPoint(const std::vector<float>& point);
    
    // Main function for Task 2
    void executeTask2();
}

// Task 3: Correspondence Matching
namespace CorrespondenceMatching {
    // Load phase maps from CSV
    cv::Mat loadPhaseMapFromCSV(const std::string& filename);
    
    // Match correspondences between left and right phase maps
    cv::Mat matchCorrespondences(const cv::Mat& leftPhaseMap, const cv::Mat& rightPhaseMap);
    
    // Compute disparity map
    cv::Mat computeDisparityMap(const cv::Mat& leftPhaseMap, const cv::Mat& rightPhaseMap);
    
    // Apply noise reduction/filtering
    cv::Mat preprocessPhaseMap(const cv::Mat& phaseMap);
    
    // Main function for Task 3
    void executeTask3();
}

// Utility functions
namespace Utils {
    // Display image with window management
    void displayImage(const cv::Mat& image, const std::string& windowName, bool waitKey = true);
    
    // Save image with proper formatting
    void saveImage(const cv::Mat& image, const std::string& filename);
    
    // Convert phase map to displayable format
    cv::Mat phaseToDisplayable(const cv::Mat& phaseMap);
    
    // Print matrix statistics
    void printMatrixStats(const cv::Mat& matrix, const std::string& name);
}

#endif // UTILS_H
