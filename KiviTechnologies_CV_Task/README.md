# Kivi Technologies Computer Vision Assignment

This project implements three computer vision algorithms for stereo structured light techniques used in 3D scanning of dental cast models.

## Project Structure

```
KiviTechnologies_CV_Task/
├── CMakeLists.txt              # CMake build configuration
├── build.sh                    # Build script
├── run.sh                      # Run script
├── README.md                   # This file
├── include/
│   └── utils.h                 # Header file with function declarations
├── src/
│   ├── main.cpp                # Main application with menu system
│   ├── utils.cpp               # Utility functions (CSV/PLY I/O)
│   ├── double_three_step.cpp   # Task 1 implementation
│   ├── bilateral_filter_3d.cpp # Task 2 implementation
│   └── correspondence_matching.cpp # Task 3 implementation
├── Tasks/                      # Original data and reference files
│   ├── CV_tasks.pdf           # Assignment description
│   ├── double-three-step/     # Gray code patterns
│   ├── 3D-bilateral/          # Point cloud data
│   ├── correspondence-matching/ # Phase map data
│   └── *.cpp                  # Original utility functions
├── build/                     # Build directory (created during build)
└── output/                    # Output files (created during execution)
```

## Requirements

- **C++17** or later
- **OpenCV 4.x** (with development headers)
- **CMake 3.10** or later
- **Make** or **Ninja** build system

### Installing Dependencies

**Ubuntu/Debian:**
```bash
sudo apt-get update
sudo apt-get install build-essential cmake libopencv-dev
```

**macOS:**
```bash
brew install cmake opencv
```

**Windows:**
- Install Visual Studio with C++ support
- Install OpenCV and set environment variables
- Install CMake

## Building the Project

1. **Quick build:**
   ```bash
   ./build.sh
   ```

2. **Manual build:**
   ```bash
   mkdir build
   cd build
   cmake ..
   make -j$(nproc)
   ```

## Running the Application

1. **Quick run:**
   ```bash
   ./run.sh
   ```

2. **Manual run:**
   ```bash
   cd build
   ./bin/KiviTechnologies_CV_Task
   ```

## Tasks Overview

### Task 1: Double Three-step Phase-shifting Algorithm
- **Objective:** Implement phase-shifting algorithm from [HHC02] paper
- **Features:**
  - Generate 6 sinusoidal patterns (1280×720, 60° phase shift)
  - Compute phase maps using Eq. 7
  - Average phase maps handling discontinuities
  - Phase unwrapping using 7 gray-coded patterns
- **Input:** Gray code patterns in `Tasks/double-three-step/`
- **Output:** Phase maps and unwrapped phase images

### Task 2: 3D Bilateral Filter for Point Clouds
- **Objective:** Implement 3D bilateral filter for organized point clouds
- **Features:**
  - Load organized point cloud (2592×1944) with normals
  - Apply bilateral filtering with 8-neighbor connectivity
  - Handle NaN values in organized point clouds
  - Export filtered results for visualization
- **Input:** `Tasks/3D-bilateral/OriginalMesh.ply`
- **Output:** Filtered point clouds (organized and unorganized formats)

### Task 3: Correspondence Matching
- **Objective:** Match pixels between left and right unwrapped phase maps
- **Features:**
  - Load noisy phase maps from CSV files
  - Apply noise reduction (median + Gaussian filtering)
  - Implement robust pixel matching algorithm
  - Generate disparity map D(xl,yl) = xl - xr
- **Input:** `Tasks/correspondence-matching/*.csv`
- **Output:** Disparity map and processed phase maps

## Menu System

The application provides an interactive menu:

1. **Task 1:** Double Three-step Phase-shifting Algorithm
2. **Task 2:** 3D Bilateral Filter for Point Clouds  
3. **Task 3:** Correspondence Matching
4. **Test utility functions** (CSV/PLY I/O testing)
5. **Exit**

## Output Files

All results are saved to the `output/` directory:

- **Task 1:**
  - `generated_pattern_*.png` - Generated sinusoidal patterns
  - `averaged_phase_map.png` - Averaged phase map
  - `unwrapped_phase_map.png` - Unwrapped phase map

- **Task 2:**
  - `filtered_mesh_unorganized.ply` - For MeshLab visualization
  - `filtered_mesh_organized.ply` - Organized format

- **Task 3:**
  - `disparity_map.png` - Disparity visualization
  - `disparity_map.csv` - Disparity data
  - `*_phase_processed.png` - Processed phase maps

## Visualization

- **Images:** Can be viewed with any image viewer
- **Point clouds:** Use MeshLab to visualize `.ply` files
- **CSV data:** Can be analyzed with Excel, MATLAB, or Python

## Implementation Notes

- All implementations use **OpenCV** for image processing
- Point cloud operations handle **NaN values** appropriately
- Phase map operations consider **discontinuities** at ±π boundaries
- Bilateral filter implements the corrected algorithm (avoiding the flaw in [FDCO03])
- Correspondence matching is optimized for **1D search** along epipolar lines

## References

- **[HHC02]** Peisen S. Huang, Qingying J. Hu, and Fu-Pen Chiang. "Double three-step phase-shifting algorithm." Applied Optics, 41(22):4503–4509, Aug 2002.
- **[FDCO03]** Shachar Fleishman, Iddo Drori, and Daniel Cohen-Or. "Bilateral mesh denoising." ACM Transactions on Graphics, 22, May 2003.

## Troubleshooting

**Build Issues:**
- Ensure OpenCV is properly installed and in PATH
- Check CMake version (minimum 3.10 required)
- Verify C++17 compiler support

**Runtime Issues:**
- Ensure data files exist in `Tasks/` directory
- Check file permissions for output directory
- Verify OpenCV runtime libraries are available

**Performance:**
- Task 2 (bilateral filter) may take several minutes for large point clouds
- Use Release build for better performance: `cmake -DCMAKE_BUILD_TYPE=Release ..`
