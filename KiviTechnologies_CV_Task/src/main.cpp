#include "../include/utils.h"
#include <iostream>
#include <string>

void printMenu()
{
    std::cout << "\n=== Kivi Technologies CV Task Menu ===" << std::endl;
    std::cout << "1. Task 1: Double Three-step Phase-shifting Algorithm" << std::endl;
    std::cout << "2. Task 2: 3D Bilateral Filter for Point Clouds" << std::endl;
    std::cout << "3. Task 3: Correspondence Matching" << std::endl;
    std::cout << "4. Test utility functions" << std::endl;
    std::cout << "5. Exit" << std::endl;
    std::cout << "Enter your choice (1-5): ";
}

void testUtilityFunctions()
{
    std::cout << "\n=== Testing Utility Functions ===" << std::endl;

    // Test CSV reading
    std::cout << "\n1. Testing CSV reading..." << std::endl;
    std::string csvPath = "data/correspondence-matching/left_uwp_map.csv";
    auto csvData = readCSV(csvPath);
    if (!csvData.empty())
    {
        std::cout << "CSV loaded successfully!" << std::endl;
        std::cout << "Dimensions: " << csvData.size() << " x " << csvData[0].size() << std::endl;
        std::cout << "First few values: ";
        for (int i = 0; i < std::min(5, (int)csvData[0].size()); i++)
        {
            std::cout << csvData[0][i] << " ";
        }
        std::cout << std::endl;
    }
    else
    {
        std::cout << "Failed to load CSV file: " << csvPath << std::endl;
    }

    // Test PLY reading
    std::cout << "\n2. Testing PLY reading..." << std::endl;
    std::string plyPath = "data/3D-bilateral/OriginalMesh.ply";
    auto pointCloud = readPLYFileWithNormals(plyPath, 2592, 1944);
    std::cout << "Point cloud dimensions: " << pointCloud.size() << " x " << pointCloud[0].size() << std::endl;

    // Test PLY writing
    std::cout << "\n3. Testing PLY writing..." << std::endl;
    writePLYFile("output/test_output.ply", pointCloud, false);

    // Test OpenCV functionality
    std::cout << "\n4. Testing OpenCV..." << std::endl;
    cv::Mat testImage = cv::Mat::zeros(100, 100, CV_8UC1);
    cv::circle(testImage, cv::Point(50, 50), 30, cv::Scalar(255), -1);
    Utils::saveImage(testImage, "output/test_circle.png");
    Utils::printMatrixStats(testImage, "Test Circle Image");

    std::cout << "\nUtility function tests completed!" << std::endl;
}

int main()
{
    std::cout << "Kivi Technologies Computer Vision Assignment" << std::endl;
    std::cout << "============================================" << std::endl;

    // Create output directory if it doesn't exist
    system("mkdir -p ../output");

    int choice;
    bool running = true;

    while (running)
    {
        printMenu();
        std::cin >> choice;

        switch (choice)
        {
        case 1:
            std::cout << "\n=== Task 1: Double Three-step Phase-shifting Algorithm ===" << std::endl;
            try
            {
                DoubleThreeStep::executeTask1();
            }
            catch (const std::exception &e)
            {
                std::cerr << "Error in Task 1: " << e.what() << std::endl;
            }
            break;

        case 2:
            std::cout << "\n=== Task 2: 3D Bilateral Filter for Point Clouds ===" << std::endl;
            try
            {
                BilateralFilter3D::executeTask2();
            }
            catch (const std::exception &e)
            {
                std::cerr << "Error in Task 2: " << e.what() << std::endl;
            }
            break;

        case 3:
            std::cout << "\n=== Task 3: Correspondence Matching ===" << std::endl;
            try
            {
                CorrespondenceMatching::executeTask3();
            }
            catch (const std::exception &e)
            {
                std::cerr << "Error in Task 3: " << e.what() << std::endl;
            }
            break;

        case 4:
            testUtilityFunctions();
            break;

        case 5:
            std::cout << "Exiting program. Goodbye!" << std::endl;
            running = false;
            break;

        default:
            std::cout << "Invalid choice. Please enter a number between 1 and 5." << std::endl;
            break;
        }

        if (running)
        {
            std::cout << "\nPress Enter to continue...";
            std::cin.ignore();
            std::cin.get();
        }
    }

    return 0;
}

// Task implementations are in separate files:
// - double_three_step.cpp
// - bilateral_filter_3d.cpp
// - correspondence_matching.cpp
