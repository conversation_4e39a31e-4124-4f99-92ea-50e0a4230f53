#include "../include/utils.h"
#include <cmath>

namespace DoubleThreeStep
{

    std::vector<cv::Mat> generateSinusoidalPatterns(int width, int height, int period)
    {
        std::vector<cv::Mat> patterns;

        // Generate 6 patterns with 60 degree phase shifts
        for (int i = 0; i < 6; i++)
        {
            cv::Mat pattern(height, width, CV_32F);
            double phaseShift = i * M_PI / 3.0; // 60 degrees in radians

            for (int y = 0; y < height; y++)
            {
                for (int x = 0; x < width; x++)
                {
                    // Sinusoidal pattern in horizontal direction
                    double value = 128.0 + 127.0 * sin(2.0 * M_PI * x / period + phaseShift);
                    pattern.at<float>(y, x) = static_cast<float>(value);
                }
            }
            patterns.push_back(pattern);
        }

        return patterns;
    }

    cv::Mat computePhaseMap(const std::vector<cv::Mat> &patterns)
    {
        // Placeholder for Eq. 7 implementation
        // This will compute phase maps from the sinusoidal patterns
        cv::Mat phaseMap = cv::Mat::zeros(patterns[0].size(), CV_32F);

        // TODO: Implement Eq. 7 from the paper
        std::cout << "Computing phase map using Eq. 7..." << std::endl;

        return phaseMap;
    }

    cv::Mat averagePhaseMaps(const cv::Mat &phaseMap1, const cv::Mat &phaseMap2)
    {
        // Placeholder for phase map averaging
        // Note: This is not simple arithmetic mean due to phase discontinuities
        cv::Mat averaged = cv::Mat::zeros(phaseMap1.size(), CV_32F);

        // TODO: Implement proper phase averaging handling discontinuities
        std::cout << "Averaging phase maps..." << std::endl;

        return averaged;
    }

    std::vector<cv::Mat> loadGrayCodePatterns(const std::string &basePath)
    {
        std::vector<cv::Mat> grayCodes;

        for (int i = 0; i < 7; i++)
        {
            std::string filename = basePath + "/gray_pattern_" + std::to_string(i) + ".png";
            cv::Mat grayCode = cv::imread(filename, cv::IMREAD_GRAYSCALE);

            if (grayCode.empty())
            {
                std::cerr << "Failed to load gray code pattern: " << filename << std::endl;
            }
            else
            {
                grayCodes.push_back(grayCode);
                std::cout << "Loaded gray code pattern " << i << ": " << filename << std::endl;
            }
        }

        return grayCodes;
    }

    cv::Mat unwrapPhase(const cv::Mat &wrappedPhase, const std::vector<cv::Mat> &grayCodes)
    {
        // Placeholder for phase unwrapping
        cv::Mat unwrapped = cv::Mat::zeros(wrappedPhase.size(), CV_32F);

        // TODO: Implement phase unwrapping using gray codes
        // Decode gray codes to get fringe orders K
        // Add 2πK to wrapped phase values
        std::cout << "Unwrapping phase using gray codes..." << std::endl;

        return unwrapped;
    }

    void executeTask1()
    {
        std::cout << "=== Executing Task 1: Double Three-step Phase-shifting Algorithm ===" << std::endl;

        try
        {
            // Step 1: Generate sinusoidal patterns
            std::cout << "\n1. Generating sinusoidal patterns..." << std::endl;
            auto patterns = generateSinusoidalPatterns(1280, 720, 10);
            std::cout << "Generated " << patterns.size() << " patterns" << std::endl;

            // Save generated patterns
            for (size_t i = 0; i < patterns.size(); i++)
            {
                cv::Mat displayPattern;
                patterns[i].convertTo(displayPattern, CV_8U);
                Utils::saveImage(displayPattern, "output/generated_pattern_" + std::to_string(i) + ".png");
            }

            // Step 2: Compute phase maps
            std::cout << "\n2. Computing phase maps..." << std::endl;
            // Split patterns into two sets for double three-step
            std::vector<cv::Mat> set1(patterns.begin(), patterns.begin() + 3);
            std::vector<cv::Mat> set2(patterns.begin() + 3, patterns.end());

            cv::Mat phaseMap1 = computePhaseMap(set1);
            cv::Mat phaseMap2 = computePhaseMap(set2);

            // Step 3: Average phase maps
            std::cout << "\n3. Averaging phase maps..." << std::endl;
            cv::Mat averagedPhase = averagePhaseMaps(phaseMap1, phaseMap2);

            // Step 4: Load gray code patterns
            std::cout << "\n4. Loading gray code patterns..." << std::endl;
            auto grayCodes = loadGrayCodePatterns("data/double-three-step");

            if (grayCodes.size() == 7)
            {
                // Step 5: Phase unwrapping
                std::cout << "\n5. Phase unwrapping..." << std::endl;
                cv::Mat unwrappedPhase = unwrapPhase(averagedPhase, grayCodes);

                // Save results
                Utils::saveImage(Utils::phaseToDisplayable(averagedPhase), "output/averaged_phase_map.png");
                Utils::saveImage(Utils::phaseToDisplayable(unwrappedPhase), "output/unwrapped_phase_map.png");

                std::cout << "\nTask 1 completed successfully!" << std::endl;
            }
            else
            {
                std::cout << "Error: Could not load all 7 gray code patterns" << std::endl;
            }
        }
        catch (const std::exception &e)
        {
            std::cerr << "Error in Task 1: " << e.what() << std::endl;
        }
    }
}
