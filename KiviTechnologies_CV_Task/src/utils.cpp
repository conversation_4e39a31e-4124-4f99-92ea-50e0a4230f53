#include "../include/utils.h"

// CSV reading function - imported from readCSV.txt.cpp
std::vector<std::vector<double>> readCSV(const std::string& filename) {
    std::ifstream file(filename);
    std::vector<std::vector<double>> matrix;
    std::string line, value;

    if (!file.is_open()) {
        std::cerr << "Error: Unable to open file " << filename << std::endl;
        return matrix;
    }

    while (std::getline(file, line)) {
        std::vector<double> row;
        std::stringstream ss(line);
        while (std::getline(ss, value, ',')) {
            try {
                row.push_back(std::stod(value));
            } catch (const std::exception& e) {
                std::cerr << "Error parsing value: " << value << std::endl;
                row.push_back(0.0); // Default value for parsing errors
            }
        }
        matrix.push_back(row);
    }
    
    file.close();
    return matrix;
}

// PLY file reading function - imported from readPLYFileWithNormals.txt.cpp
std::vector<std::vector<std::vector<float>>> readPLYFileWithNormals(const std::string& filename, int width, int height) {
    std::ifstream inFile(filename);
    std::vector<std::vector<std::vector<float>>> organized_point_cloud(width, std::vector<std::vector<float>>(height, std::vector<float>(6, std::numeric_limits<float>::quiet_NaN())));  // Initialize with NaN values

    std::string line;

    if (!inFile) {
        std::cerr << "Unable to open file: " << filename << std::endl;
        return organized_point_cloud;  // return empty opc
    }

    // Read and skip header lines until we reach end_header
    while (std::getline(inFile, line)) {
        if (line == "end_header") {
            break;
        }
    }

    int nan = 0;
    int no_nan = 0;
    int current_row = 0, current_col = 0;

    // Read data lines
    while (std::getline(inFile, line) && current_row < width) {
        std::istringstream lineStream(line);
        float x, y, z, nx, ny, nz;

        // If parsing succeeds and none of the values are NaN
        if ((lineStream >> x >> y >> z >> nx >> ny >> nz)) {
            organized_point_cloud[current_row][current_col] = { x, y, z, nx, ny, nz };
            no_nan++;
        }
        else {
            nan++;
        }

        current_col++;
        if (current_col >= height) {
            current_col = 0;
            current_row++;
        }
    }

    std::cout << "PLY file loaded - NaN points: " << nan << ", Valid points: " << no_nan << std::endl;
    inFile.close();
    return organized_point_cloud;
}

// PLY file writing function - imported from writePLYFile.txt.cpp
void writePLYFile(const std::string& filename, const std::vector<std::vector<std::vector<float>>>& organized_point_cloud, bool organized) {
    std::ofstream plyFile(filename);

    if (!plyFile) {
        std::cout << "Unable to open file: " << filename << std::endl;
        return;
    }

    // Calculate total vertices
    size_t totalVertices = 0;
    if (!organized){
        for (const auto& row : organized_point_cloud) {
            for (const auto& pt : row) {
                if (!std::isnan(pt[0])) { // assuming if x is NaN, the entire point is invalid
                    totalVertices++;
                }
            }
        }
    } 
    else {
        totalVertices = organized_point_cloud.size() * organized_point_cloud[0].size();
    }
   
    plyFile << "ply\n";
    plyFile << "format ascii 1.0\n";
    plyFile << "element vertex " << totalVertices << "\n";
    plyFile << "property float x\n";
    plyFile << "property float y\n";
    plyFile << "property float z\n";
    plyFile << "property float nx\n";
    plyFile << "property float ny\n";
    plyFile << "property float nz\n";
    plyFile << "element face " << 0 << "\n";
    plyFile << "property list uchar int vertex_indices\n";
    plyFile << "end_header\n";

    for (const auto& row : organized_point_cloud) {
        for (const auto& pt : row) {
            if (organized || !std::isnan(pt[0])) { // assuming if x is NaN, the entire point is invalid
                plyFile << pt[0] << " " << pt[1] << " " << pt[2] << " "
                    << pt[3] << " " << pt[4] << " " << pt[5] << "\n";
            }
        }
    }
    
    plyFile.close();
    std::cout << "PLY file written: " << filename << " with " << totalVertices << " vertices" << std::endl;
}

// Utility functions
namespace Utils {
    void displayImage(const cv::Mat& image, const std::string& windowName, bool waitKey) {
        cv::namedWindow(windowName, cv::WINDOW_AUTOSIZE);
        cv::imshow(windowName, image);
        if (waitKey) {
            cv::waitKey(0);
            cv::destroyWindow(windowName);
        }
    }
    
    void saveImage(const cv::Mat& image, const std::string& filename) {
        cv::imwrite(filename, image);
        std::cout << "Image saved: " << filename << std::endl;
    }
    
    cv::Mat phaseToDisplayable(const cv::Mat& phaseMap) {
        cv::Mat displayable;
        cv::normalize(phaseMap, displayable, 0, 255, cv::NORM_MINMAX, CV_8UC1);
        return displayable;
    }
    
    void printMatrixStats(const cv::Mat& matrix, const std::string& name) {
        double minVal, maxVal;
        cv::minMaxLoc(matrix, &minVal, &maxVal);
        cv::Scalar meanVal = cv::mean(matrix);
        std::cout << name << " - Min: " << minVal << ", Max: " << maxVal 
                  << ", Mean: " << meanVal[0] << ", Size: " << matrix.size() << std::endl;
    }
}
