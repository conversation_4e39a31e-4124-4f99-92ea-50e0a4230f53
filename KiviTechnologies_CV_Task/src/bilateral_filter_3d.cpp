#include "../include/utils.h"
#include <cmath>

namespace BilateralFilter3D
{

    bool isValidPoint(const std::vector<float> &point)
    {
        return !std::isnan(point[0]) && !std::isnan(point[1]) && !std::isnan(point[2]);
    }

    double computeDistance(const std::vector<float> &p1, const std::vector<float> &p2)
    {
        if (!isValidPoint(p1) || !isValidPoint(p2))
        {
            return std::numeric_limits<double>::infinity();
        }

        double dx = p1[0] - p2[0];
        double dy = p1[1] - p2[1];
        double dz = p1[2] - p2[2];

        return sqrt(dx * dx + dy * dy + dz * dz);
    }

    double computeNormalDistance(const std::vector<float> &n1, const std::vector<float> &n2)
    {
        if (std::isnan(n1[3]) || std::isnan(n1[4]) || std::isnan(n1[5]) ||
            std::isnan(n2[3]) || std::isnan(n2[4]) || std::isnan(n2[5]))
        {
            return std::numeric_limits<double>::infinity();
        }

        // Compute dot product of normals
        double dot = n1[3] * n2[3] + n1[4] * n2[4] + n1[5] * n2[5];

        // Clamp to [-1, 1] to avoid numerical issues with acos
        dot = std::max(-1.0, std::min(1.0, dot));

        // Return angular distance
        return acos(abs(dot));
    }

    std::vector<std::vector<std::vector<float>>> applyBilateralFilter(
        const std::vector<std::vector<std::vector<float>>> &pointCloud,
        double spatialSigma, double normalSigma)
    {

        int width = pointCloud.size();
        int height = pointCloud[0].size();

        // Initialize filtered point cloud
        std::vector<std::vector<std::vector<float>>> filtered(width,
                                                              std::vector<std::vector<float>>(height,
                                                                                              std::vector<float>(6, std::numeric_limits<float>::quiet_NaN())));

        std::cout << "Applying 3D bilateral filter..." << std::endl;
        std::cout << "Spatial sigma: " << spatialSigma << ", Normal sigma: " << normalSigma << std::endl;

        int processedPoints = 0;
        int totalValidPoints = 0;

        // Process each point
        for (int x = 0; x < width; x++)
        {
            for (int y = 0; y < height; y++)
            {
                const auto &centerPoint = pointCloud[x][y];

                // Skip invalid points
                if (!isValidPoint(centerPoint))
                {
                    continue;
                }

                totalValidPoints++;

                // Initialize weighted sum and weight sum
                std::vector<double> weightedSum(6, 0.0);
                double totalWeight = 0.0;

                // Consider 8-neighborhood
                for (int dx = -1; dx <= 1; dx++)
                {
                    for (int dy = -1; dy <= 1; dy++)
                    {
                        int nx = x + dx;
                        int ny = y + dy;

                        // Check bounds
                        if (nx < 0 || nx >= width || ny < 0 || ny >= height)
                        {
                            continue;
                        }

                        const auto &neighborPoint = pointCloud[nx][ny];

                        // Skip invalid neighbors
                        if (!isValidPoint(neighborPoint))
                        {
                            continue;
                        }

                        // Compute spatial weight
                        double spatialDist = computeDistance(centerPoint, neighborPoint);
                        double spatialWeight = exp(-(spatialDist * spatialDist) / (2.0 * spatialSigma * spatialSigma));

                        // Compute normal weight
                        double normalDist = computeNormalDistance(centerPoint, neighborPoint);
                        double normalWeight = exp(-(normalDist * normalDist) / (2.0 * normalSigma * normalSigma));

                        // Combined weight
                        double weight = spatialWeight * normalWeight;

                        // Accumulate weighted sum
                        for (int i = 0; i < 6; i++)
                        {
                            weightedSum[i] += weight * neighborPoint[i];
                        }
                        totalWeight += weight;
                    }
                }

                // Normalize and store result
                if (totalWeight > 0)
                {
                    for (int i = 0; i < 6; i++)
                    {
                        filtered[x][y][i] = static_cast<float>(weightedSum[i] / totalWeight);
                    }
                    processedPoints++;
                }
            }

            // Progress indicator
            if (x % 100 == 0)
            {
                std::cout << "Processed " << x << "/" << width << " rows" << std::endl;
            }
        }

        std::cout << "Bilateral filtering completed!" << std::endl;
        std::cout << "Processed " << processedPoints << " out of " << totalValidPoints << " valid points" << std::endl;

        return filtered;
    }

    void executeTask2()
    {
        std::cout << "=== Executing Task 2: 3D Bilateral Filter for Point Clouds ===" << std::endl;

        try
        {
            // Step 1: Load organized point cloud
            std::cout << "\n1. Loading organized point cloud..." << std::endl;
            std::string plyPath = "data/3D-bilateral/OriginalMesh.ply";
            auto originalPointCloud = readPLYFileWithNormals(plyPath, 2592, 1944);

            if (originalPointCloud.empty())
            {
                std::cerr << "Failed to load point cloud from: " << plyPath << std::endl;
                return;
            }

            // Step 2: Apply bilateral filter
            std::cout << "\n2. Applying 3D bilateral filter..." << std::endl;

            // Filter parameters (these may need tuning)
            double spatialSigma = 0.5; // Spatial smoothing parameter
            double normalSigma = 0.1;  // Normal smoothing parameter

            auto filteredPointCloud = applyBilateralFilter(originalPointCloud, spatialSigma, normalSigma);

            // Step 3: Save results
            std::cout << "\n3. Saving filtered point cloud..." << std::endl;

            // Save as unorganized point cloud for visualization
            writePLYFile("output/filtered_mesh_unorganized.ply", filteredPointCloud, false);

            // Save as organized point cloud for further processing
            writePLYFile("output/filtered_mesh_organized.ply", filteredPointCloud, true);

            std::cout << "\nTask 2 completed successfully!" << std::endl;
            std::cout << "Results saved to:" << std::endl;
            std::cout << "- output/filtered_mesh_unorganized.ply (for MeshLab visualization)" << std::endl;
            std::cout << "- output/filtered_mesh_organized.ply (organized format)" << std::endl;
        }
        catch (const std::exception &e)
        {
            std::cerr << "Error in Task 2: " << e.what() << std::endl;
        }
    }
}
