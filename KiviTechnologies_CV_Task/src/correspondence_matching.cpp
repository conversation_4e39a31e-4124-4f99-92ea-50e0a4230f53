#include "../include/utils.h"
#include <algorithm>

namespace CorrespondenceMatching
{

    cv::Mat loadPhaseMapFromCSV(const std::string &filename)
    {
        auto csvData = readCSV(filename);

        if (csvData.empty())
        {
            std::cerr << "Failed to load CSV: " << filename << std::endl;
            return cv::Mat();
        }

        int rows = csvData.size();
        int cols = csvData[0].size();

        cv::Mat phaseMap(rows, cols, CV_64F);

        for (int i = 0; i < rows; i++)
        {
            for (int j = 0; j < cols; j++)
            {
                phaseMap.at<double>(i, j) = csvData[i][j];
            }
        }

        std::cout << "Loaded phase map from " << filename << " - Size: " << rows << "x" << cols << std::endl;
        Utils::printMatrixStats(phaseMap, "Phase Map");

        return phaseMap;
    }

    cv::Mat preprocessPhaseMap(const cv::Mat &phaseMap)
    {
        cv::Mat processed;
        phaseMap.copyTo(processed);

        // Apply noise reduction filters
        // 1. Median filter to remove impulse noise
        cv::Mat temp;
        processed.convertTo(temp, CV_32F);
        cv::medianBlur(temp, temp, 3);

        // 2. Gaussian filter to reduce Gaussian noise
        cv::GaussianBlur(temp, temp, cv::Size(3, 3), 0.5);

        temp.convertTo(processed, CV_64F);

        std::cout << "Applied noise reduction preprocessing" << std::endl;

        return processed;
    }

    cv::Mat matchCorrespondences(const cv::Mat &leftPhaseMap, const cv::Mat &rightPhaseMap)
    {
        int rows = leftPhaseMap.rows;
        int cols = leftPhaseMap.cols;

        cv::Mat correspondences = cv::Mat::zeros(rows, cols, CV_32S);

        std::cout << "Matching correspondences..." << std::endl;

        // For each pixel in left image, find best match in right image (same row)
        for (int y = 0; y < rows; y++)
        {
            for (int x = 0; x < cols; x++)
            {
                double leftValue = leftPhaseMap.at<double>(y, x);

                // Skip invalid values
                if (std::isnan(leftValue) || std::isinf(leftValue))
                {
                    correspondences.at<int>(y, x) = -1; // No correspondence
                    continue;
                }

                double bestMatch = std::numeric_limits<double>::max();
                int bestX = -1;

                // Search along the same row in right image
                for (int xr = 0; xr < cols; xr++)
                {
                    double rightValue = rightPhaseMap.at<double>(y, xr);

                    // Skip invalid values
                    if (std::isnan(rightValue) || std::isinf(rightValue))
                    {
                        continue;
                    }

                    // Compute absolute difference
                    double diff = abs(leftValue - rightValue);

                    if (diff < bestMatch)
                    {
                        bestMatch = diff;
                        bestX = xr;
                    }
                }

                correspondences.at<int>(y, x) = bestX;
            }

            // Progress indicator
            if (y % 50 == 0)
            {
                std::cout << "Processed row " << y << "/" << rows << std::endl;
            }
        }

        std::cout << "Correspondence matching completed" << std::endl;

        return correspondences;
    }

    cv::Mat computeDisparityMap(const cv::Mat &leftPhaseMap, const cv::Mat &rightPhaseMap)
    {
        // First find correspondences
        cv::Mat correspondences = matchCorrespondences(leftPhaseMap, rightPhaseMap);

        int rows = leftPhaseMap.rows;
        int cols = leftPhaseMap.cols;

        cv::Mat disparityMap = cv::Mat::zeros(rows, cols, CV_32F);

        std::cout << "Computing disparity map..." << std::endl;

        int validDisparities = 0;
        double sumDisparity = 0.0;

        for (int y = 0; y < rows; y++)
        {
            for (int x = 0; x < cols; x++)
            {
                int xr = correspondences.at<int>(y, x);

                if (xr >= 0)
                {
                    // Disparity = xl - xr
                    float disparity = static_cast<float>(x - xr);
                    disparityMap.at<float>(y, x) = disparity;

                    validDisparities++;
                    sumDisparity += abs(disparity);
                }
                else
                {
                    // No valid correspondence found
                    disparityMap.at<float>(y, x) = std::numeric_limits<float>::quiet_NaN();
                }
            }
        }

        double meanAbsDisparity = validDisparities > 0 ? sumDisparity / validDisparities : 0.0;

        std::cout << "Disparity map computed!" << std::endl;
        std::cout << "Valid disparities: " << validDisparities << "/" << (rows * cols) << std::endl;
        std::cout << "Mean absolute disparity: " << meanAbsDisparity << std::endl;

        Utils::printMatrixStats(disparityMap, "Disparity Map");

        return disparityMap;
    }

    void executeTask3()
    {
        std::cout << "=== Executing Task 3: Correspondence Matching ===" << std::endl;

        try
        {
            // Step 1: Load phase maps
            std::cout << "\n1. Loading unwrapped phase maps..." << std::endl;

            std::string leftPath = "data/correspondence-matching/left_uwp_map.csv";
            std::string rightPath = "data/correspondence-matching/right_uwp_map.csv";

            cv::Mat leftPhaseMap = loadPhaseMapFromCSV(leftPath);
            cv::Mat rightPhaseMap = loadPhaseMapFromCSV(rightPath);

            if (leftPhaseMap.empty() || rightPhaseMap.empty())
            {
                std::cerr << "Failed to load phase maps" << std::endl;
                return;
            }

            // Step 2: Preprocess phase maps to reduce noise
            std::cout << "\n2. Preprocessing phase maps..." << std::endl;
            cv::Mat leftProcessed = preprocessPhaseMap(leftPhaseMap);
            cv::Mat rightProcessed = preprocessPhaseMap(rightPhaseMap);

            // Step 3: Compute disparity map
            std::cout << "\n3. Computing disparity map..." << std::endl;
            cv::Mat disparityMap = computeDisparityMap(leftProcessed, rightProcessed);

            // Step 4: Save results
            std::cout << "\n4. Saving results..." << std::endl;

            // Save disparity map as image
            cv::Mat disparityDisplay;
            cv::normalize(disparityMap, disparityDisplay, 0, 255, cv::NORM_MINMAX, CV_8UC1);
            Utils::saveImage(disparityDisplay, "output/disparity_map.png");

            // Save disparity map as CSV for analysis
            std::ofstream csvFile("output/disparity_map.csv");
            if (csvFile.is_open())
            {
                for (int y = 0; y < disparityMap.rows; y++)
                {
                    for (int x = 0; x < disparityMap.cols; x++)
                    {
                        float disparity = disparityMap.at<float>(y, x);
                        csvFile << disparity;
                        if (x < disparityMap.cols - 1)
                            csvFile << ",";
                    }
                    csvFile << "\n";
                }
                csvFile.close();
                std::cout << "Disparity map saved as CSV: output/disparity_map.csv" << std::endl;
            }

            // Save processed phase maps for visualization
            Utils::saveImage(Utils::phaseToDisplayable(leftProcessed), "output/left_phase_processed.png");
            Utils::saveImage(Utils::phaseToDisplayable(rightProcessed), "output/right_phase_processed.png");

            std::cout << "\nTask 3 completed successfully!" << std::endl;
            std::cout << "Results saved to:" << std::endl;
            std::cout << "- output/disparity_map.png (visualization)" << std::endl;
            std::cout << "- output/disparity_map.csv (data)" << std::endl;
            std::cout << "- output/left_phase_processed.png" << std::endl;
            std::cout << "- output/right_phase_processed.png" << std::endl;
        }
        catch (const std::exception &e)
        {
            std::cerr << "Error in Task 3: " << e.what() << std::endl;
        }
    }
}
