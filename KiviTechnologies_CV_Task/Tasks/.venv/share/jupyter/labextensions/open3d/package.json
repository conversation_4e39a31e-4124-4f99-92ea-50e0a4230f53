{"name": "open3d", "version": "0.19.0", "description": "Open3D: A Modern Library for 3D Data Processing.", "author": "<EMAIL>", "main": "lib/index.js", "repository": {"type": "git", "url": "https://github.com/isl-org/Open3D.git"}, "license": "MIT", "keywords": ["jup<PERSON><PERSON>", "widgets", "ipython", "ipywidgets", "jupyterlab-extension"], "files": ["lib/**/*.js", "dist/*.js"], "scripts": {"clean": "rimraf dist/ && rimraf ../open3d/labextension/ && rimraf ../open3d/nbextension", "prepublish": "yarn run clean && yarn run build:prod", "build": "webpack --mode=development && yarn run build:labextension:dev", "build:prod": "webpack --mode=production && yarn run build:labextension", "build:labextension": "jupyter labextension build .", "build:labextension:dev": "jupyter labextension build --development True .", "watch": "webpack --watch --mode=development", "test": "echo \"Error: no test specified\" && exit 1"}, "devDependencies": {"@jupyterlab/builder": "^3.0.0", "webpack": "^5", "rimraf": "^2.6.1"}, "dependencies": {"@jupyter-widgets/base": "^2 || ^3 || ^4 || ^5 || ^6", "lodash": "^4.17.4", "webrtc-adapter": "^4.2.2"}, "jupyterlab": {"extension": "lib/labplugin", "outputDir": "../open3d/labextension", "sharedPackages": {"@jupyter-widgets/base": {"bundled": false, "singleton": true}}, "_build": {"load": "static/remoteEntry.978ca237b2e98d7edb06.js", "extension": "./extension"}}}