var _JUPYTERLAB;(()=>{"use strict";var e,r,t,n,o,a,i,s,u,d,f,l,c,p,h,v,g,m,b,y,w,E,k,S={324:(e,r,t)=>{var n={"./index":()=>t.e(509).then((()=>()=>t(509))),"./extension":()=>Promise.all([t.e(199),t.e(493)]).then((()=>()=>t(493)))},o=(e,r)=>(t.R=r,r=t.o(n,e)?n[e]():Promise.resolve().then((()=>{throw new Error('Module "'+e+'" does not exist in container.')})),t.R=void 0,r),a=(e,r)=>{if(t.S){var n="default",o=t.S[n];if(o&&o!==e)throw new Error("Container initialization failed as it has already been initialized with a different share scope");return t.S[n]=e,t.I(n,r)}};t.d(r,{get:()=>o,init:()=>a})}},j={};function P(e){var r=j[e];if(void 0!==r)return r.exports;var t=j[e]={id:e,loaded:!1,exports:{}};return S[e].call(t.exports,t,t.exports,P),t.loaded=!0,t.exports}P.m=S,P.c=j,P.d=(e,r)=>{for(var t in r)P.o(r,t)&&!P.o(e,t)&&Object.defineProperty(e,t,{enumerable:!0,get:r[t]})},P.f={},P.e=e=>Promise.all(Object.keys(P.f).reduce(((r,t)=>(P.f[t](e,r),r)),[])),P.u=e=>e+"."+{493:"f9f9e1870a89c440e367",509:"cd497512212c7d678181",543:"1c80835729e2b26eb76d",752:"c371c3dc80b47b0e5256"}[e]+".js?v="+{493:"f9f9e1870a89c440e367",509:"cd497512212c7d678181",543:"1c80835729e2b26eb76d",752:"c371c3dc80b47b0e5256"}[e],P.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),P.o=(e,r)=>Object.prototype.hasOwnProperty.call(e,r),e={},r="open3d:",P.l=(t,n,o,a)=>{if(e[t])e[t].push(n);else{var i,s;if(void 0!==o)for(var u=document.getElementsByTagName("script"),d=0;d<u.length;d++){var f=u[d];if(f.getAttribute("src")==t||f.getAttribute("data-webpack")==r+o){i=f;break}}i||(s=!0,(i=document.createElement("script")).charset="utf-8",i.timeout=120,P.nc&&i.setAttribute("nonce",P.nc),i.setAttribute("data-webpack",r+o),i.src=t),e[t]=[n];var l=(r,n)=>{i.onerror=i.onload=null,clearTimeout(c);var o=e[t];if(delete e[t],i.parentNode&&i.parentNode.removeChild(i),o&&o.forEach((e=>e(n))),r)return r(n)},c=setTimeout(l.bind(null,void 0,{type:"timeout",target:i}),12e4);i.onerror=l.bind(null,i.onerror),i.onload=l.bind(null,i.onload),s&&document.head.appendChild(i)}},P.nmd=e=>(e.paths=[],e.children||(e.children=[]),e),(()=>{P.S={};var e={},r={};P.I=(t,n)=>{n||(n=[]);var o=r[t];if(o||(o=r[t]={}),!(n.indexOf(o)>=0)){if(n.push(o),e[t])return e[t];P.o(P.S,t)||(P.S[t]={});var a=P.S[t],i="open3d",s=(e,r,t,n)=>{var o=a[e]=a[e]||{},s=o[r];(!s||!s.loaded&&(!n!=!s.eager?n:i>s.from))&&(o[r]={get:t,from:i,eager:!!n})},u=[];return"default"===t&&(s("lodash","4.17.21",(()=>P.e(543).then((()=>()=>P(543))))),s("open3d","0.19.0",(()=>P.e(509).then((()=>()=>P(509))))),s("webrtc-adapter","4.2.2",(()=>P.e(752).then((()=>()=>P(752)))))),e[t]=u.length?Promise.all(u).then((()=>e[t]=1)):1}}})(),(()=>{var e;P.g.importScripts&&(e=P.g.location+"");var r=P.g.document;if(!e&&r&&(r.currentScript&&"SCRIPT"===r.currentScript.tagName.toUpperCase()&&(e=r.currentScript.src),!e)){var t=r.getElementsByTagName("script");if(t.length)for(var n=t.length-1;n>-1&&(!e||!/^http(s?):/.test(e));)e=t[n--].src}if(!e)throw new Error("Automatic publicPath is not supported in this browser");e=e.replace(/#.*$/,"").replace(/\?.*$/,"").replace(/\/[^\/]+$/,"/"),P.p=e})(),t=e=>{var r=e=>e.split(".").map((e=>+e==e?+e:e)),t=/^([^-+]+)?(?:-([^+]+))?(?:\+(.+))?$/.exec(e),n=t[1]?r(t[1]):[];return t[2]&&(n.length++,n.push.apply(n,r(t[2]))),t[3]&&(n.push([]),n.push.apply(n,r(t[3]))),n},n=(e,r)=>{e=t(e),r=t(r);for(var n=0;;){if(n>=e.length)return n<r.length&&"u"!=(typeof r[n])[0];var o=e[n],a=(typeof o)[0];if(n>=r.length)return"u"==a;var i=r[n],s=(typeof i)[0];if(a!=s)return"o"==a&&"n"==s||"s"==s||"u"==a;if("o"!=a&&"u"!=a&&o!=i)return o<i;n++}},o=e=>{var r=e[0],t="";if(1===e.length)return"*";if(r+.5){t+=0==r?">=":-1==r?"<":1==r?"^":2==r?"~":r>0?"=":"!=";for(var n=1,a=1;a<e.length;a++)n--,t+="u"==(typeof(s=e[a]))[0]?"-":(n>0?".":"")+(n=2,s);return t}var i=[];for(a=1;a<e.length;a++){var s=e[a];i.push(0===s?"not("+u()+")":1===s?"("+u()+" || "+u()+")":2===s?i.pop()+" "+i.pop():o(s))}return u();function u(){return i.pop().replace(/^\((.+)\)$/,"$1")}},a=(e,r)=>{if(0 in e){r=t(r);var n=e[0],o=n<0;o&&(n=-n-1);for(var i=0,s=1,u=!0;;s++,i++){var d,f,l=s<e.length?(typeof e[s])[0]:"";if(i>=r.length||"o"==(f=(typeof(d=r[i]))[0]))return!u||("u"==l?s>n&&!o:""==l!=o);if("u"==f){if(!u||"u"!=l)return!1}else if(u)if(l==f)if(s<=n){if(d!=e[s])return!1}else{if(o?d>e[s]:d<e[s])return!1;d!=e[s]&&(u=!1)}else if("s"!=l&&"n"!=l){if(o||s<=n)return!1;u=!1,s--}else{if(s<=n||f<l!=o)return!1;u=!1}else"s"!=l&&"n"!=l&&(u=!1,s--)}}var c=[],p=c.pop.bind(c);for(i=1;i<e.length;i++){var h=e[i];c.push(1==h?p()|p():2==h?p()&p():h?a(h,r):!p())}return!!p()},i=(e,r)=>e&&P.o(e,r),s=e=>(e.loaded=1,e.get()),u=e=>Object.keys(e).reduce(((r,t)=>(e[t].eager&&(r[t]=e[t]),r)),{}),d=(e,r,t,o)=>{var i=o?u(e[r]):e[r];return(r=Object.keys(i).reduce(((e,r)=>!a(t,r)||e&&!n(e,r)?e:r),0))&&i[r]},f=(e,r,t)=>{var o=t?u(e[r]):e[r];return Object.keys(o).reduce(((e,r)=>!e||!o[e].loaded&&n(e,r)?r:e),0)},l=(e,r,t,n)=>"Unsatisfied version "+t+" from "+(t&&e[r][t].from)+" of shared singleton module "+r+" (required "+o(n)+")",c=(e,r,t,n,a)=>{var i=e[t];return"No satisfying version ("+o(n)+")"+(a?" for eager consumption":"")+" of shared module "+t+" found in shared scope "+r+".\nAvailable versions: "+Object.keys(i).map((e=>e+" from "+i[e].from)).join(", ")},p=e=>{throw new Error(e)},h=e=>{"undefined"!=typeof console&&console.warn&&console.warn(e)},g=(e,r,t)=>t?t():((e,r)=>p("Shared module "+r+" doesn't exist in shared scope "+e))(e,r),m=(v=e=>function(r,t,n,o,a){var i=P.I(r);return i&&i.then&&!n?i.then(e.bind(e,r,P.S[r],t,!1,o,a)):e(r,P.S[r],t,n,o,a)})(((e,r,t,n,o,a)=>{if(!i(r,t))return g(e,t,a);var u=d(r,t,o,n);return u?s(u):a?a():void p(c(r,e,t,o,n))})),b=v(((e,r,t,n,o,u)=>{if(!i(r,t))return g(e,t,u);var d=f(r,t,n);return a(o,d)||h(l(r,t,d,o)),s(r[t][d])})),y={},w={42:()=>m("default","lodash",!1,[1,4,17,4],(()=>P.e(543).then((()=>()=>P(543))))),488:()=>b("default","@jupyter-widgets/base",!1,[,[1,6],[1,5],[1,4],[1,3],[1,2],1,1,1,1]),981:()=>m("default","webrtc-adapter",!1,[1,4,2,2],(()=>P.e(752).then((()=>()=>P(752)))))},E={199:[42,488,981],509:[42,488,981]},k={},P.f.consumes=(e,r)=>{P.o(E,e)&&E[e].forEach((e=>{if(P.o(y,e))return r.push(y[e]);if(!k[e]){var t=r=>{y[e]=0,P.m[e]=t=>{delete P.c[e],t.exports=r()}};k[e]=!0;var n=r=>{delete y[e],P.m[e]=t=>{throw delete P.c[e],r}};try{var o=w[e]();o.then?r.push(y[e]=o.then(t).catch(n)):t(o)}catch(e){n(e)}}}))},(()=>{var e={154:0};P.f.j=(r,t)=>{var n=P.o(e,r)?e[r]:void 0;if(0!==n)if(n)t.push(n[2]);else if(199!=r){var o=new Promise(((t,o)=>n=e[r]=[t,o]));t.push(n[2]=o);var a=P.p+P.u(r),i=new Error;P.l(a,(t=>{if(P.o(e,r)&&(0!==(n=e[r])&&(e[r]=void 0),n)){var o=t&&("load"===t.type?"missing":t.type),a=t&&t.target&&t.target.src;i.message="Loading chunk "+r+" failed.\n("+o+": "+a+")",i.name="ChunkLoadError",i.type=o,i.request=a,n[1](i)}}),"chunk-"+r,r)}else e[r]=0};var r=(r,t)=>{var n,o,[a,i,s]=t,u=0;if(a.some((r=>0!==e[r]))){for(n in i)P.o(i,n)&&(P.m[n]=i[n]);s&&s(P)}for(r&&r(t);u<a.length;u++)o=a[u],P.o(e,o)&&e[o]&&e[o][0](),e[o]=0},t=self.webpackChunkopen3d=self.webpackChunkopen3d||[];t.forEach(r.bind(null,0)),t.push=r.bind(null,t.push.bind(t))})();var x=P(324);(_JUPYTERLAB=void 0===_JUPYTERLAB?{}:_JUPYTERLAB).open3d=x})();