"use strict";(self.webpackChunkopen3d=self.webpackChunkopen3d||[]).push([[752],{963:e=>{var t={generateIdentifier:function(){return Math.random().toString(36).substr(2,10)}};t.localCName=t.generateIdentifier(),t.splitLines=function(e){return e.trim().split("\n").map((function(e){return e.trim()}))},t.splitSections=function(e){return e.split("\nm=").map((function(e,t){return(t>0?"m="+e:e).trim()+"\r\n"}))},t.getDescription=function(e){var r=t.splitSections(e);return r&&r[0]},t.getMediaSections=function(e){var r=t.splitSections(e);return r.shift(),r},t.matchPrefix=function(e,r){return t.splitLines(e).filter((function(e){return 0===e.indexOf(r)}))},t.parseCandidate=function(e){for(var t,r={foundation:(t=0===e.indexOf("a=candidate:")?e.substring(12).split(" "):e.substring(10).split(" "))[0],component:parseInt(t[1],10),protocol:t[2].toLowerCase(),priority:parseInt(t[3],10),ip:t[4],address:t[4],port:parseInt(t[5],10),type:t[7]},n=8;n<t.length;n+=2)switch(t[n]){case"raddr":r.relatedAddress=t[n+1];break;case"rport":r.relatedPort=parseInt(t[n+1],10);break;case"tcptype":r.tcpType=t[n+1];break;case"ufrag":r.ufrag=t[n+1],r.usernameFragment=t[n+1];break;default:r[t[n]]=t[n+1]}return r},t.writeCandidate=function(e){var t=[];t.push(e.foundation),t.push(e.component),t.push(e.protocol.toUpperCase()),t.push(e.priority),t.push(e.address||e.ip),t.push(e.port);var r=e.type;return t.push("typ"),t.push(r),"host"!==r&&e.relatedAddress&&e.relatedPort&&(t.push("raddr"),t.push(e.relatedAddress),t.push("rport"),t.push(e.relatedPort)),e.tcpType&&"tcp"===e.protocol.toLowerCase()&&(t.push("tcptype"),t.push(e.tcpType)),(e.usernameFragment||e.ufrag)&&(t.push("ufrag"),t.push(e.usernameFragment||e.ufrag)),"candidate:"+t.join(" ")},t.parseIceOptions=function(e){return e.substr(14).split(" ")},t.parseRtpMap=function(e){var t=e.substr(9).split(" "),r={payloadType:parseInt(t.shift(),10)};return t=t[0].split("/"),r.name=t[0],r.clockRate=parseInt(t[1],10),r.channels=3===t.length?parseInt(t[2],10):1,r.numChannels=r.channels,r},t.writeRtpMap=function(e){var t=e.payloadType;void 0!==e.preferredPayloadType&&(t=e.preferredPayloadType);var r=e.channels||e.numChannels||1;return"a=rtpmap:"+t+" "+e.name+"/"+e.clockRate+(1!==r?"/"+r:"")+"\r\n"},t.parseExtmap=function(e){var t=e.substr(9).split(" ");return{id:parseInt(t[0],10),direction:t[0].indexOf("/")>0?t[0].split("/")[1]:"sendrecv",uri:t[1]}},t.writeExtmap=function(e){return"a=extmap:"+(e.id||e.preferredId)+(e.direction&&"sendrecv"!==e.direction?"/"+e.direction:"")+" "+e.uri+"\r\n"},t.parseFmtp=function(e){for(var t,r={},n=e.substr(e.indexOf(" ")+1).split(";"),i=0;i<n.length;i++)r[(t=n[i].trim().split("="))[0].trim()]=t[1];return r},t.writeFmtp=function(e){var t="",r=e.payloadType;if(void 0!==e.preferredPayloadType&&(r=e.preferredPayloadType),e.parameters&&Object.keys(e.parameters).length){var n=[];Object.keys(e.parameters).forEach((function(t){e.parameters[t]?n.push(t+"="+e.parameters[t]):n.push(t)})),t+="a=fmtp:"+r+" "+n.join(";")+"\r\n"}return t},t.parseRtcpFb=function(e){var t=e.substr(e.indexOf(" ")+1).split(" ");return{type:t.shift(),parameter:t.join(" ")}},t.writeRtcpFb=function(e){var t="",r=e.payloadType;return void 0!==e.preferredPayloadType&&(r=e.preferredPayloadType),e.rtcpFeedback&&e.rtcpFeedback.length&&e.rtcpFeedback.forEach((function(e){t+="a=rtcp-fb:"+r+" "+e.type+(e.parameter&&e.parameter.length?" "+e.parameter:"")+"\r\n"})),t},t.parseSsrcMedia=function(e){var t=e.indexOf(" "),r={ssrc:parseInt(e.substr(7,t-7),10)},n=e.indexOf(":",t);return n>-1?(r.attribute=e.substr(t+1,n-t-1),r.value=e.substr(n+1)):r.attribute=e.substr(t+1),r},t.parseSsrcGroup=function(e){var t=e.substr(13).split(" ");return{semantics:t.shift(),ssrcs:t.map((function(e){return parseInt(e,10)}))}},t.getMid=function(e){var r=t.matchPrefix(e,"a=mid:")[0];if(r)return r.substr(6)},t.parseFingerprint=function(e){var t=e.substr(14).split(" ");return{algorithm:t[0].toLowerCase(),value:t[1]}},t.getDtlsParameters=function(e,r){return{role:"auto",fingerprints:t.matchPrefix(e+r,"a=fingerprint:").map(t.parseFingerprint)}},t.writeDtlsParameters=function(e,t){var r="a=setup:"+t+"\r\n";return e.fingerprints.forEach((function(e){r+="a=fingerprint:"+e.algorithm+" "+e.value+"\r\n"})),r},t.parseCryptoLine=function(e){var t=e.substr(9).split(" ");return{tag:parseInt(t[0],10),cryptoSuite:t[1],keyParams:t[2],sessionParams:t.slice(3)}},t.writeCryptoLine=function(e){return"a=crypto:"+e.tag+" "+e.cryptoSuite+" "+("object"==typeof e.keyParams?t.writeCryptoKeyParams(e.keyParams):e.keyParams)+(e.sessionParams?" "+e.sessionParams.join(" "):"")+"\r\n"},t.parseCryptoKeyParams=function(e){if(0!==e.indexOf("inline:"))return null;var t=e.substr(7).split("|");return{keyMethod:"inline",keySalt:t[0],lifeTime:t[1],mkiValue:t[2]?t[2].split(":")[0]:void 0,mkiLength:t[2]?t[2].split(":")[1]:void 0}},t.writeCryptoKeyParams=function(e){return e.keyMethod+":"+e.keySalt+(e.lifeTime?"|"+e.lifeTime:"")+(e.mkiValue&&e.mkiLength?"|"+e.mkiValue+":"+e.mkiLength:"")},t.getCryptoParameters=function(e,r){return t.matchPrefix(e+r,"a=crypto:").map(t.parseCryptoLine)},t.getIceParameters=function(e,r){var n=t.matchPrefix(e+r,"a=ice-ufrag:")[0],i=t.matchPrefix(e+r,"a=ice-pwd:")[0];return n&&i?{usernameFragment:n.substr(12),password:i.substr(10)}:null},t.writeIceParameters=function(e){return"a=ice-ufrag:"+e.usernameFragment+"\r\na=ice-pwd:"+e.password+"\r\n"},t.parseRtpParameters=function(e){for(var r={codecs:[],headerExtensions:[],fecMechanisms:[],rtcp:[]},n=t.splitLines(e)[0].split(" "),i=3;i<n.length;i++){var a=n[i],o=t.matchPrefix(e,"a=rtpmap:"+a+" ")[0];if(o){var s=t.parseRtpMap(o),c=t.matchPrefix(e,"a=fmtp:"+a+" ");switch(s.parameters=c.length?t.parseFmtp(c[0]):{},s.rtcpFeedback=t.matchPrefix(e,"a=rtcp-fb:"+a+" ").map(t.parseRtcpFb),r.codecs.push(s),s.name.toUpperCase()){case"RED":case"ULPFEC":r.fecMechanisms.push(s.name.toUpperCase())}}}return t.matchPrefix(e,"a=extmap:").forEach((function(e){r.headerExtensions.push(t.parseExtmap(e))})),r},t.writeRtpDescription=function(e,r){var n="";n+="m="+e+" ",n+=r.codecs.length>0?"9":"0",n+=" UDP/TLS/RTP/SAVPF ",n+=r.codecs.map((function(e){return void 0!==e.preferredPayloadType?e.preferredPayloadType:e.payloadType})).join(" ")+"\r\n",n+="c=IN IP4 0.0.0.0\r\n",n+="a=rtcp:9 IN IP4 0.0.0.0\r\n",r.codecs.forEach((function(e){n+=t.writeRtpMap(e),n+=t.writeFmtp(e),n+=t.writeRtcpFb(e)}));var i=0;return r.codecs.forEach((function(e){e.maxptime>i&&(i=e.maxptime)})),i>0&&(n+="a=maxptime:"+i+"\r\n"),n+="a=rtcp-mux\r\n",r.headerExtensions&&r.headerExtensions.forEach((function(e){n+=t.writeExtmap(e)})),n},t.parseRtpEncodingParameters=function(e){var r,n=[],i=t.parseRtpParameters(e),a=-1!==i.fecMechanisms.indexOf("RED"),o=-1!==i.fecMechanisms.indexOf("ULPFEC"),s=t.matchPrefix(e,"a=ssrc:").map((function(e){return t.parseSsrcMedia(e)})).filter((function(e){return"cname"===e.attribute})),c=s.length>0&&s[0].ssrc,d=t.matchPrefix(e,"a=ssrc-group:FID").map((function(e){return e.substr(17).split(" ").map((function(e){return parseInt(e,10)}))}));d.length>0&&d[0].length>1&&d[0][0]===c&&(r=d[0][1]),i.codecs.forEach((function(e){if("RTX"===e.name.toUpperCase()&&e.parameters.apt){var t={ssrc:c,codecPayloadType:parseInt(e.parameters.apt,10)};c&&r&&(t.rtx={ssrc:r}),n.push(t),a&&((t=JSON.parse(JSON.stringify(t))).fec={ssrc:c,mechanism:o?"red+ulpfec":"red"},n.push(t))}})),0===n.length&&c&&n.push({ssrc:c});var p=t.matchPrefix(e,"b=");return p.length&&(p=0===p[0].indexOf("b=TIAS:")?parseInt(p[0].substr(7),10):0===p[0].indexOf("b=AS:")?1e3*parseInt(p[0].substr(5),10)*.95-16e3:void 0,n.forEach((function(e){e.maxBitrate=p}))),n},t.parseRtcpParameters=function(e){var r={},n=t.matchPrefix(e,"a=ssrc:").map((function(e){return t.parseSsrcMedia(e)})).filter((function(e){return"cname"===e.attribute}))[0];n&&(r.cname=n.value,r.ssrc=n.ssrc);var i=t.matchPrefix(e,"a=rtcp-rsize");r.reducedSize=i.length>0,r.compound=0===i.length;var a=t.matchPrefix(e,"a=rtcp-mux");return r.mux=a.length>0,r},t.parseMsid=function(e){var r,n=t.matchPrefix(e,"a=msid:");if(1===n.length)return{stream:(r=n[0].substr(7).split(" "))[0],track:r[1]};var i=t.matchPrefix(e,"a=ssrc:").map((function(e){return t.parseSsrcMedia(e)})).filter((function(e){return"msid"===e.attribute}));return i.length>0?{stream:(r=i[0].value.split(" "))[0],track:r[1]}:void 0},t.parseSctpDescription=function(e){var r,n=t.parseMLine(e),i=t.matchPrefix(e,"a=max-message-size:");i.length>0&&(r=parseInt(i[0].substr(19),10)),isNaN(r)&&(r=65536);var a=t.matchPrefix(e,"a=sctp-port:");if(a.length>0)return{port:parseInt(a[0].substr(12),10),protocol:n.fmt,maxMessageSize:r};if(t.matchPrefix(e,"a=sctpmap:").length>0){var o=t.matchPrefix(e,"a=sctpmap:")[0].substr(10).split(" ");return{port:parseInt(o[0],10),protocol:o[1],maxMessageSize:r}}},t.writeSctpDescription=function(e,t){var r=[];return r="DTLS/SCTP"!==e.protocol?["m="+e.kind+" 9 "+e.protocol+" "+t.protocol+"\r\n","c=IN IP4 0.0.0.0\r\n","a=sctp-port:"+t.port+"\r\n"]:["m="+e.kind+" 9 "+e.protocol+" "+t.port+"\r\n","c=IN IP4 0.0.0.0\r\n","a=sctpmap:"+t.port+" "+t.protocol+" 65535\r\n"],void 0!==t.maxMessageSize&&r.push("a=max-message-size:"+t.maxMessageSize+"\r\n"),r.join("")},t.generateSessionId=function(){return Math.random().toString().substr(2,21)},t.writeSessionBoilerplate=function(e,r,n){var i=void 0!==r?r:2;return"v=0\r\no="+(n||"thisisadapterortc")+" "+(e||t.generateSessionId())+" "+i+" IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\n"},t.writeMediaSection=function(e,r,n,i){var a=t.writeRtpDescription(e.kind,r);if(a+=t.writeIceParameters(e.iceGatherer.getLocalParameters()),a+=t.writeDtlsParameters(e.dtlsTransport.getLocalParameters(),"offer"===n?"actpass":"active"),a+="a=mid:"+e.mid+"\r\n",e.direction?a+="a="+e.direction+"\r\n":e.rtpSender&&e.rtpReceiver?a+="a=sendrecv\r\n":e.rtpSender?a+="a=sendonly\r\n":e.rtpReceiver?a+="a=recvonly\r\n":a+="a=inactive\r\n",e.rtpSender){var o="msid:"+i.id+" "+e.rtpSender.track.id+"\r\n";a+="a="+o,a+="a=ssrc:"+e.sendEncodingParameters[0].ssrc+" "+o,e.sendEncodingParameters[0].rtx&&(a+="a=ssrc:"+e.sendEncodingParameters[0].rtx.ssrc+" "+o,a+="a=ssrc-group:FID "+e.sendEncodingParameters[0].ssrc+" "+e.sendEncodingParameters[0].rtx.ssrc+"\r\n")}return a+="a=ssrc:"+e.sendEncodingParameters[0].ssrc+" cname:"+t.localCName+"\r\n",e.rtpSender&&e.sendEncodingParameters[0].rtx&&(a+="a=ssrc:"+e.sendEncodingParameters[0].rtx.ssrc+" cname:"+t.localCName+"\r\n"),a},t.getDirection=function(e,r){for(var n=t.splitLines(e),i=0;i<n.length;i++)switch(n[i]){case"a=sendrecv":case"a=sendonly":case"a=recvonly":case"a=inactive":return n[i].substr(2)}return r?t.getDirection(r):"sendrecv"},t.getKind=function(e){return t.splitLines(e)[0].split(" ")[0].substr(2)},t.isRejected=function(e){return"0"===e.split(" ",2)[1]},t.parseMLine=function(e){var r=t.splitLines(e)[0].substr(2).split(" ");return{kind:r[0],port:parseInt(r[1],10),protocol:r[2],fmt:r.slice(3).join(" ")}},t.parseOLine=function(e){var r=t.matchPrefix(e,"o=")[0].substr(2).split(" ");return{username:r[0],sessionId:r[1],sessionVersion:parseInt(r[2],10),netType:r[3],addressType:r[4],address:r[5]}},t.isValidSDP=function(e){if("string"!=typeof e||0===e.length)return!1;for(var r=t.splitLines(e),n=0;n<r.length;n++)if(r[n].length<2||"="!==r[n].charAt(1))return!1;return!0},e.exports=t},752:(e,t,r)=>{var n=r(525);e.exports=n({window:r.g.window})},525:(e,t,r)=>{e.exports=function(e,t){var n=e&&e.window,i={shimChrome:!0,shimFirefox:!0,shimEdge:!0,shimSafari:!0};for(var a in t)hasOwnProperty.call(t,a)&&(i[a]=t[a]);var o=r(634),s=o.log,c=o.detectBrowser(n),d={browserDetails:c,extractVersion:o.extractVersion,disableLog:o.disableLog,disableWarnings:o.disableWarnings},p=r(528)||null,u=r(190)||null,l=r(542)||null,f=r(824)||null;switch(c.browser){case"chrome":if(!p||!p.shimPeerConnection||!i.shimChrome)return s("Chrome shim is not included in this adapter release."),d;s("adapter.js shimming chrome."),d.browserShim=p,p.shimGetUserMedia(n),p.shimMediaStream(n),o.shimCreateObjectURL(n),p.shimSourceObject(n),p.shimPeerConnection(n),p.shimOnTrack(n),p.shimAddTrackRemoveTrack(n),p.shimGetSendersWithDtmf(n);break;case"firefox":if(!l||!l.shimPeerConnection||!i.shimFirefox)return s("Firefox shim is not included in this adapter release."),d;s("adapter.js shimming firefox."),d.browserShim=l,l.shimGetUserMedia(n),o.shimCreateObjectURL(n),l.shimSourceObject(n),l.shimPeerConnection(n),l.shimOnTrack(n);break;case"edge":if(!u||!u.shimPeerConnection||!i.shimEdge)return s("MS edge shim is not included in this adapter release."),d;s("adapter.js shimming edge."),d.browserShim=u,u.shimGetUserMedia(n),o.shimCreateObjectURL(n),u.shimPeerConnection(n),u.shimReplaceTrack(n);break;case"safari":if(!f||!i.shimSafari)return s("Safari shim is not included in this adapter release."),d;s("adapter.js shimming safari."),d.browserShim=f,o.shimCreateObjectURL(n),f.shimRTCIceServerUrls(n),f.shimCallbacksAPI(n),f.shimLocalStreamsAPI(n),f.shimRemoteStreamsAPI(n),f.shimGetUserMedia(n);break;default:s("Unsupported browser!")}return d}},528:(e,t,r)=>{var n=r(634),i=n.log,a={shimMediaStream:function(e){e.MediaStream=e.MediaStream||e.webkitMediaStream},shimOnTrack:function(e){if("object"==typeof e&&e.RTCPeerConnection&&!("ontrack"in e.RTCPeerConnection.prototype)){Object.defineProperty(e.RTCPeerConnection.prototype,"ontrack",{get:function(){return this._ontrack},set:function(e){this._ontrack&&this.removeEventListener("track",this._ontrack),this.addEventListener("track",this._ontrack=e)}});var t=e.RTCPeerConnection.prototype.setRemoteDescription;e.RTCPeerConnection.prototype.setRemoteDescription=function(){var r=this;return r._ontrackpoly||(r._ontrackpoly=function(t){t.stream.addEventListener("addtrack",(function(n){var i;i=e.RTCPeerConnection.prototype.getReceivers?r.getReceivers().find((function(e){return e.track&&e.track.id===n.track.id})):{track:n.track};var a=new Event("track");a.track=n.track,a.receiver=i,a.streams=[t.stream],r.dispatchEvent(a)})),t.stream.getTracks().forEach((function(n){var i;i=e.RTCPeerConnection.prototype.getReceivers?r.getReceivers().find((function(e){return e.track&&e.track.id===n.id})):{track:n};var a=new Event("track");a.track=n,a.receiver=i,a.streams=[t.stream],r.dispatchEvent(a)}))},r.addEventListener("addstream",r._ontrackpoly)),t.apply(r,arguments)}}},shimGetSendersWithDtmf:function(e){if("object"==typeof e&&e.RTCPeerConnection&&!("getSenders"in e.RTCPeerConnection.prototype)&&"createDTMFSender"in e.RTCPeerConnection.prototype){var t=function(e,t){return{track:t,get dtmf(){return void 0===this._dtmf&&("audio"===t.kind?this._dtmf=e.createDTMFSender(t):this._dtmf=null),this._dtmf},_pc:e}};if(!e.RTCPeerConnection.prototype.getSenders){e.RTCPeerConnection.prototype.getSenders=function(){return this._senders=this._senders||[],this._senders.slice()};var r=e.RTCPeerConnection.prototype.addTrack;e.RTCPeerConnection.prototype.addTrack=function(e,n){var i=this,a=r.apply(i,arguments);return a||(a=t(i,e),i._senders.push(a)),a};var n=e.RTCPeerConnection.prototype.removeTrack;e.RTCPeerConnection.prototype.removeTrack=function(e){var t=this;n.apply(t,arguments);var r=t._senders.indexOf(e);-1!==r&&t._senders.splice(r,1)}}var i=e.RTCPeerConnection.prototype.addStream;e.RTCPeerConnection.prototype.addStream=function(e){var r=this;r._senders=r._senders||[],i.apply(r,[e]),e.getTracks().forEach((function(e){r._senders.push(t(r,e))}))};var a=e.RTCPeerConnection.prototype.removeStream;e.RTCPeerConnection.prototype.removeStream=function(e){var t=this;t._senders=t._senders||[],a.apply(t,[t._streams[e.id]||e]),e.getTracks().forEach((function(e){var r=t._senders.find((function(t){return t.track===e}));r&&t._senders.splice(t._senders.indexOf(r),1)}))}}else if("object"==typeof e&&e.RTCPeerConnection&&"getSenders"in e.RTCPeerConnection.prototype&&"createDTMFSender"in e.RTCPeerConnection.prototype&&e.RTCRtpSender&&!("dtmf"in e.RTCRtpSender.prototype)){var o=e.RTCPeerConnection.prototype.getSenders;e.RTCPeerConnection.prototype.getSenders=function(){var e=this,t=o.apply(e,[]);return t.forEach((function(t){t._pc=e})),t},Object.defineProperty(e.RTCRtpSender.prototype,"dtmf",{get:function(){return void 0===this._dtmf&&("audio"===this.track.kind?this._dtmf=this._pc.createDTMFSender(this.track):this._dtmf=null),this._dtmf}})}},shimSourceObject:function(e){var t=e&&e.URL;"object"==typeof e&&e.HTMLMediaElement&&!("srcObject"in e.HTMLMediaElement.prototype)&&Object.defineProperty(e.HTMLMediaElement.prototype,"srcObject",{get:function(){return this._srcObject},set:function(e){var r=this;this._srcObject=e,this.src&&t.revokeObjectURL(this.src),e?(this.src=t.createObjectURL(e),e.addEventListener("addtrack",(function(){r.src&&t.revokeObjectURL(r.src),r.src=t.createObjectURL(e)})),e.addEventListener("removetrack",(function(){r.src&&t.revokeObjectURL(r.src),r.src=t.createObjectURL(e)}))):this.src=""}})},shimAddTrackRemoveTrack:function(e){if(!e.RTCPeerConnection.prototype.addTrack){var t=e.RTCPeerConnection.prototype.getLocalStreams;e.RTCPeerConnection.prototype.getLocalStreams=function(){var e=this,r=t.apply(this);return e._reverseStreams=e._reverseStreams||{},r.map((function(t){return e._reverseStreams[t.id]}))};var r=e.RTCPeerConnection.prototype.addStream;e.RTCPeerConnection.prototype.addStream=function(t){var n=this;if(n._streams=n._streams||{},n._reverseStreams=n._reverseStreams||{},t.getTracks().forEach((function(e){if(n.getSenders().find((function(t){return t.track===e})))throw new DOMException("Track already exists.","InvalidAccessError")})),!n._reverseStreams[t.id]){var i=new e.MediaStream(t.getTracks());n._streams[t.id]=i,n._reverseStreams[i.id]=t,t=i}r.apply(n,[t])};var n=e.RTCPeerConnection.prototype.removeStream;e.RTCPeerConnection.prototype.removeStream=function(e){var t=this;t._streams=t._streams||{},t._reverseStreams=t._reverseStreams||{},n.apply(t,[t._streams[e.id]||e]),delete t._reverseStreams[t._streams[e.id]?t._streams[e.id].id:e.id],delete t._streams[e.id]},e.RTCPeerConnection.prototype.addTrack=function(t,r){var n=this;if("closed"===n.signalingState)throw new DOMException("The RTCPeerConnection's signalingState is 'closed'.","InvalidStateError");var i=[].slice.call(arguments,1);if(1!==i.length||!i[0].getTracks().find((function(e){return e===t})))throw new DOMException("The adapter.js addTrack polyfill only supports a single  stream which is associated with the specified track.","NotSupportedError");if(n.getSenders().find((function(e){return e.track===t})))throw new DOMException("Track already exists.","InvalidAccessError");n._streams=n._streams||{},n._reverseStreams=n._reverseStreams||{};var a=n._streams[r.id];if(a)a.addTrack(t),n.dispatchEvent(new Event("negotiationneeded"));else{var o=new e.MediaStream([t]);n._streams[r.id]=o,n._reverseStreams[o.id]=r,n.addStream(o)}return n.getSenders().find((function(e){return e.track===t}))},e.RTCPeerConnection.prototype.removeTrack=function(e){var t,r=this;if("closed"===r.signalingState)throw new DOMException("The RTCPeerConnection's signalingState is 'closed'.","InvalidStateError");if(!e._pc)throw new DOMException("Argument 1 of RTCPeerConnection.removeTrack does not implement interface RTCRtpSender.","TypeError");if(e._pc!==r)throw new DOMException("Sender was not created by this connection.","InvalidAccessError");r._streams=r._streams||{},Object.keys(r._streams).forEach((function(n){r._streams[n].getTracks().find((function(t){return e.track===t}))&&(t=r._streams[n])})),t&&(1===t.getTracks().length?r.removeStream(t):t.removeTrack(e.track),r.dispatchEvent(new Event("negotiationneeded")))}}},shimPeerConnection:function(e){var t=n.detectBrowser(e);if(e.RTCPeerConnection){var r=e.RTCPeerConnection;e.RTCPeerConnection=function(e,t){if(e&&e.iceServers){for(var i=[],a=0;a<e.iceServers.length;a++){var o=e.iceServers[a];!o.hasOwnProperty("urls")&&o.hasOwnProperty("url")?(n.deprecated("RTCIceServer.url","RTCIceServer.urls"),(o=JSON.parse(JSON.stringify(o))).urls=o.url,i.push(o)):i.push(e.iceServers[a])}e.iceServers=i}return new r(e,t)},e.RTCPeerConnection.prototype=r.prototype,Object.defineProperty(e.RTCPeerConnection,"generateCertificate",{get:function(){return r.generateCertificate}})}else e.RTCPeerConnection=function(t,r){return i("PeerConnection"),t&&t.iceTransportPolicy&&(t.iceTransports=t.iceTransportPolicy),new e.webkitRTCPeerConnection(t,r)},e.RTCPeerConnection.prototype=e.webkitRTCPeerConnection.prototype,e.webkitRTCPeerConnection.generateCertificate&&Object.defineProperty(e.RTCPeerConnection,"generateCertificate",{get:function(){return e.webkitRTCPeerConnection.generateCertificate}});var a=e.RTCPeerConnection.prototype.getStats;e.RTCPeerConnection.prototype.getStats=function(e,t,r){var n=this,i=arguments;if(arguments.length>0&&"function"==typeof e)return a.apply(this,arguments);if(0===a.length&&(0===arguments.length||"function"!=typeof arguments[0]))return a.apply(this,[]);var o=function(e){var t={};return e.result().forEach((function(e){var r={id:e.id,timestamp:e.timestamp,type:{localcandidate:"local-candidate",remotecandidate:"remote-candidate"}[e.type]||e.type};e.names().forEach((function(t){r[t]=e.stat(t)})),t[r.id]=r})),t},s=function(e){return new Map(Object.keys(e).map((function(t){return[t,e[t]]})))};return arguments.length>=2?a.apply(this,[function(e){i[1](s(o(e)))},arguments[0]]):new Promise((function(e,t){a.apply(n,[function(t){e(s(o(t)))},t])})).then(t,r)},t.version<51&&["setLocalDescription","setRemoteDescription","addIceCandidate"].forEach((function(t){var r=e.RTCPeerConnection.prototype[t];e.RTCPeerConnection.prototype[t]=function(){var e=arguments,t=this,n=new Promise((function(n,i){r.apply(t,[e[0],n,i])}));return e.length<2?n:n.then((function(){e[1].apply(null,[])}),(function(t){e.length>=3&&e[2].apply(null,[t])}))}})),t.version<52&&["createOffer","createAnswer"].forEach((function(t){var r=e.RTCPeerConnection.prototype[t];e.RTCPeerConnection.prototype[t]=function(){var e=this;if(arguments.length<1||1===arguments.length&&"object"==typeof arguments[0]){var t=1===arguments.length?arguments[0]:void 0;return new Promise((function(n,i){r.apply(e,[n,i,t])}))}return r.apply(this,arguments)}})),["setLocalDescription","setRemoteDescription","addIceCandidate"].forEach((function(t){var r=e.RTCPeerConnection.prototype[t];e.RTCPeerConnection.prototype[t]=function(){return arguments[0]=new("addIceCandidate"===t?e.RTCIceCandidate:e.RTCSessionDescription)(arguments[0]),r.apply(this,arguments)}}));var o=e.RTCPeerConnection.prototype.addIceCandidate;e.RTCPeerConnection.prototype.addIceCandidate=function(){return arguments[0]?o.apply(this,arguments):(arguments[1]&&arguments[1].apply(null),Promise.resolve())}}};e.exports={shimMediaStream:a.shimMediaStream,shimOnTrack:a.shimOnTrack,shimAddTrackRemoveTrack:a.shimAddTrackRemoveTrack,shimGetSendersWithDtmf:a.shimGetSendersWithDtmf,shimSourceObject:a.shimSourceObject,shimPeerConnection:a.shimPeerConnection,shimGetUserMedia:r(293)}},293:(e,t,r)=>{var n=r(634),i=n.log;e.exports=function(e){var t=n.detectBrowser(e),r=e&&e.navigator,a=function(e){if("object"!=typeof e||e.mandatory||e.optional)return e;var t={};return Object.keys(e).forEach((function(r){if("require"!==r&&"advanced"!==r&&"mediaSource"!==r){var n="object"==typeof e[r]?e[r]:{ideal:e[r]};void 0!==n.exact&&"number"==typeof n.exact&&(n.min=n.max=n.exact);var i=function(e,t){return e?e+t.charAt(0).toUpperCase()+t.slice(1):"deviceId"===t?"sourceId":t};if(void 0!==n.ideal){t.optional=t.optional||[];var a={};"number"==typeof n.ideal?(a[i("min",r)]=n.ideal,t.optional.push(a),(a={})[i("max",r)]=n.ideal,t.optional.push(a)):(a[i("",r)]=n.ideal,t.optional.push(a))}void 0!==n.exact&&"number"!=typeof n.exact?(t.mandatory=t.mandatory||{},t.mandatory[i("",r)]=n.exact):["min","max"].forEach((function(e){void 0!==n[e]&&(t.mandatory=t.mandatory||{},t.mandatory[i(e,r)]=n[e])}))}})),e.advanced&&(t.optional=(t.optional||[]).concat(e.advanced)),t},o=function(e,n){if((e=JSON.parse(JSON.stringify(e)))&&"object"==typeof e.audio){var o=function(e,t,r){t in e&&!(r in e)&&(e[r]=e[t],delete e[t])};o((e=JSON.parse(JSON.stringify(e))).audio,"autoGainControl","googAutoGainControl"),o(e.audio,"noiseSuppression","googNoiseSuppression"),e.audio=a(e.audio)}if(e&&"object"==typeof e.video){var s=e.video.facingMode;s=s&&("object"==typeof s?s:{ideal:s});var c,d=t.version<61;if(s&&("user"===s.exact||"environment"===s.exact||"user"===s.ideal||"environment"===s.ideal)&&(!r.mediaDevices.getSupportedConstraints||!r.mediaDevices.getSupportedConstraints().facingMode||d)&&(delete e.video.facingMode,"environment"===s.exact||"environment"===s.ideal?c=["back","rear"]:"user"!==s.exact&&"user"!==s.ideal||(c=["front"]),c))return r.mediaDevices.enumerateDevices().then((function(t){var r=(t=t.filter((function(e){return"videoinput"===e.kind}))).find((function(e){return c.some((function(t){return-1!==e.label.toLowerCase().indexOf(t)}))}));return!r&&t.length&&-1!==c.indexOf("back")&&(r=t[t.length-1]),r&&(e.video.deviceId=s.exact?{exact:r.deviceId}:{ideal:r.deviceId}),e.video=a(e.video),i("chrome: "+JSON.stringify(e)),n(e)}));e.video=a(e.video)}return i("chrome: "+JSON.stringify(e)),n(e)},s=function(e){return{name:{PermissionDeniedError:"NotAllowedError",InvalidStateError:"NotReadableError",DevicesNotFoundError:"NotFoundError",ConstraintNotSatisfiedError:"OverconstrainedError",TrackStartError:"NotReadableError",MediaDeviceFailedDueToShutdown:"NotReadableError",MediaDeviceKillSwitchOn:"NotReadableError"}[e.name]||e.name,message:e.message,constraint:e.constraintName,toString:function(){return this.name+(this.message&&": ")+this.message}}};r.getUserMedia=function(e,t,n){o(e,(function(e){r.webkitGetUserMedia(e,t,(function(e){n(s(e))}))}))};var c=function(e){return new Promise((function(t,n){r.getUserMedia(e,t,n)}))};if(r.mediaDevices||(r.mediaDevices={getUserMedia:c,enumerateDevices:function(){return new Promise((function(t){var r={audio:"audioinput",video:"videoinput"};return e.MediaStreamTrack.getSources((function(e){t(e.map((function(e){return{label:e.label,kind:r[e.kind],deviceId:e.id,groupId:""}})))}))}))},getSupportedConstraints:function(){return{deviceId:!0,echoCancellation:!0,facingMode:!0,frameRate:!0,height:!0,width:!0}}}),r.mediaDevices.getUserMedia){var d=r.mediaDevices.getUserMedia.bind(r.mediaDevices);r.mediaDevices.getUserMedia=function(e){return o(e,(function(e){return d(e).then((function(t){if(e.audio&&!t.getAudioTracks().length||e.video&&!t.getVideoTracks().length)throw t.getTracks().forEach((function(e){e.stop()})),new DOMException("","NotFoundError");return t}),(function(e){return Promise.reject(s(e))}))}))}}else r.mediaDevices.getUserMedia=function(e){return c(e)};void 0===r.mediaDevices.addEventListener&&(r.mediaDevices.addEventListener=function(){i("Dummy mediaDevices.addEventListener called.")}),void 0===r.mediaDevices.removeEventListener&&(r.mediaDevices.removeEventListener=function(){i("Dummy mediaDevices.removeEventListener called.")})}},190:(e,t,r)=>{var n=r(634),i=r(928);e.exports={shimGetUserMedia:r(684),shimPeerConnection:function(e){var t=n.detectBrowser(e);if(e.RTCIceGatherer&&(e.RTCIceCandidate||(e.RTCIceCandidate=function(e){return e}),e.RTCSessionDescription||(e.RTCSessionDescription=function(e){return e}),t.version<15025)){var r=Object.getOwnPropertyDescriptor(e.MediaStreamTrack.prototype,"enabled");Object.defineProperty(e.MediaStreamTrack.prototype,"enabled",{set:function(e){r.set.call(this,e);var t=new Event("enabled");t.enabled=e,this.dispatchEvent(t)}})}e.RTCRtpSender&&!("dtmf"in e.RTCRtpSender.prototype)&&Object.defineProperty(e.RTCRtpSender.prototype,"dtmf",{get:function(){return void 0===this._dtmf&&("audio"===this.track.kind?this._dtmf=new e.RTCDtmfSender(this):"video"===this.track.kind&&(this._dtmf=null)),this._dtmf}}),e.RTCPeerConnection=i(e,t.version)},shimReplaceTrack:function(e){e.RTCRtpSender&&!("replaceTrack"in e.RTCRtpSender.prototype)&&(e.RTCRtpSender.prototype.replaceTrack=e.RTCRtpSender.prototype.setTrack)}}},684:e=>{e.exports=function(e){var t=e&&e.navigator,r=t.mediaDevices.getUserMedia.bind(t.mediaDevices);t.mediaDevices.getUserMedia=function(e){return r(e).catch((function(e){return Promise.reject(function(e){return{name:{PermissionDeniedError:"NotAllowedError"}[e.name]||e.name,message:e.message,constraint:e.constraint,toString:function(){return this.name}}}(e))}))}}},928:(e,t,r)=>{var n=r(963);function i(e,t){var r={codecs:[],headerExtensions:[],fecMechanisms:[]},n=function(e,t){e=parseInt(e,10);for(var r=0;r<t.length;r++)if(t[r].payloadType===e||t[r].preferredPayloadType===e)return t[r]},i=function(e,t,r,i){var a=n(e.parameters.apt,r),o=n(t.parameters.apt,i);return a&&o&&a.name.toLowerCase()===o.name.toLowerCase()};return e.codecs.forEach((function(n){for(var a=0;a<t.codecs.length;a++){var o=t.codecs[a];if(n.name.toLowerCase()===o.name.toLowerCase()&&n.clockRate===o.clockRate){if("rtx"===n.name.toLowerCase()&&n.parameters&&o.parameters.apt&&!i(n,o,e.codecs,t.codecs))continue;(o=JSON.parse(JSON.stringify(o))).numChannels=Math.min(n.numChannels,o.numChannels),r.codecs.push(o),o.rtcpFeedback=o.rtcpFeedback.filter((function(e){for(var t=0;t<n.rtcpFeedback.length;t++)if(n.rtcpFeedback[t].type===e.type&&n.rtcpFeedback[t].parameter===e.parameter)return!0;return!1}));break}}})),e.headerExtensions.forEach((function(e){for(var n=0;n<t.headerExtensions.length;n++){var i=t.headerExtensions[n];if(e.uri===i.uri){r.headerExtensions.push(i);break}}})),r}function a(e,t,r){return-1!=={offer:{setLocalDescription:["stable","have-local-offer"],setRemoteDescription:["stable","have-remote-offer"]},answer:{setLocalDescription:["have-remote-offer","have-local-pranswer"],setRemoteDescription:["have-local-offer","have-remote-pranswer"]}}[t][e].indexOf(r)}e.exports=function(e,t){var r=function(r){var i=this,a=document.createDocumentFragment();if(["addEventListener","removeEventListener","dispatchEvent"].forEach((function(e){i[e]=a[e].bind(a)})),this.needNegotiation=!1,this.onicecandidate=null,this.onaddstream=null,this.ontrack=null,this.onremovestream=null,this.onsignalingstatechange=null,this.oniceconnectionstatechange=null,this.onicegatheringstatechange=null,this.onnegotiationneeded=null,this.ondatachannel=null,this.canTrickleIceCandidates=null,this.localStreams=[],this.remoteStreams=[],this.getLocalStreams=function(){return i.localStreams},this.getRemoteStreams=function(){return i.remoteStreams},this.localDescription=new e.RTCSessionDescription({type:"",sdp:""}),this.remoteDescription=new e.RTCSessionDescription({type:"",sdp:""}),this.signalingState="stable",this.iceConnectionState="new",this.iceGatheringState="new",this.iceOptions={gatherPolicy:"all",iceServers:[]},r&&r.iceTransportPolicy)switch(r.iceTransportPolicy){case"all":case"relay":this.iceOptions.gatherPolicy=r.iceTransportPolicy}this.usingBundle=r&&"max-bundle"===r.bundlePolicy,r&&r.iceServers&&(this.iceOptions.iceServers=function(e,t){var r=!1;return(e=JSON.parse(JSON.stringify(e))).filter((function(e){if(e&&(e.urls||e.url)){var n=e.urls||e.url;e.url&&!e.urls&&console.warn("RTCIceServer.url is deprecated! Use urls instead.");var i="string"==typeof n;return i&&(n=[n]),n=n.filter((function(e){return 0!==e.indexOf("turn:")||-1===e.indexOf("transport=udp")||-1!==e.indexOf("turn:[")||r?0===e.indexOf("stun:")&&t>=14393:(r=!0,!0)})),delete e.url,e.urls=i?n[0]:n,!!n.length}return!1}))}(r.iceServers,t)),this._config=r||{},this.transceivers=[],this._localIceCandidatesBuffer=[],this._sdpSessionId=n.generateSessionId()};return r.prototype._emitGatheringStateChange=function(){var e=new Event("icegatheringstatechange");this.dispatchEvent(e),null!==this.onicegatheringstatechange&&this.onicegatheringstatechange(e)},r.prototype._emitBufferedCandidates=function(){var e=this,t=n.splitSections(e.localDescription.sdp);this._localIceCandidatesBuffer.forEach((function(r){if(r.candidate&&0!==Object.keys(r.candidate).length)t[r.candidate.sdpMLineIndex+1]+="a="+r.candidate.candidate+"\r\n";else for(var n=1;n<t.length;n++)-1===t[n].indexOf("\r\na=end-of-candidates\r\n")&&(t[n]+="a=end-of-candidates\r\n");e.localDescription.sdp=t.join(""),e.dispatchEvent(r),null!==e.onicecandidate&&e.onicecandidate(r),r.candidate||"complete"===e.iceGatheringState||e.transceivers.every((function(e){return e.iceGatherer&&"completed"===e.iceGatherer.state}))&&"complete"!==e.iceGatheringStateChange&&(e.iceGatheringState="complete",e._emitGatheringStateChange())})),this._localIceCandidatesBuffer=[]},r.prototype.getConfiguration=function(){return this._config},r.prototype._createTransceiver=function(e){var t=this.transceivers.length>0,r={track:null,iceGatherer:null,iceTransport:null,dtlsTransport:null,localCapabilities:null,remoteCapabilities:null,rtpSender:null,rtpReceiver:null,kind:e,mid:null,sendEncodingParameters:null,recvEncodingParameters:null,stream:null,wantReceive:!0};if(this.usingBundle&&t)r.iceTransport=this.transceivers[0].iceTransport,r.dtlsTransport=this.transceivers[0].dtlsTransport;else{var n=this._createIceAndDtlsTransports();r.iceTransport=n.iceTransport,r.dtlsTransport=n.dtlsTransport}return this.transceivers.push(r),r},r.prototype.addTrack=function(t,r){for(var n,i=0;i<this.transceivers.length;i++)this.transceivers[i].track||this.transceivers[i].kind!==t.kind||(n=this.transceivers[i]);return n||(n=this._createTransceiver(t.kind)),n.track=t,n.stream=r,n.rtpSender=new e.RTCRtpSender(t,n.dtlsTransport),this._maybeFireNegotiationNeeded(),n.rtpSender},r.prototype.addStream=function(e){var r=this;if(t>=15025)this.localStreams.push(e),e.getTracks().forEach((function(t){r.addTrack(t,e)}));else{var n=e.clone();e.getTracks().forEach((function(e,t){var r=n.getTracks()[t];e.addEventListener("enabled",(function(e){r.enabled=e.enabled}))})),n.getTracks().forEach((function(e){r.addTrack(e,n)})),this.localStreams.push(n)}this._maybeFireNegotiationNeeded()},r.prototype.removeStream=function(e){var t=this.localStreams.indexOf(e);t>-1&&(this.localStreams.splice(t,1),this._maybeFireNegotiationNeeded())},r.prototype.getSenders=function(){return this.transceivers.filter((function(e){return!!e.rtpSender})).map((function(e){return e.rtpSender}))},r.prototype.getReceivers=function(){return this.transceivers.filter((function(e){return!!e.rtpReceiver})).map((function(e){return e.rtpReceiver}))},r.prototype._createIceGatherer=function(t,r){var i=this,a=new e.RTCIceGatherer(i.iceOptions);return a.onlocalcandidate=function(e){var o=new Event("icecandidate");o.candidate={sdpMid:t,sdpMLineIndex:r};var s=e.candidate,c=!s||0===Object.keys(s).length;c?void 0===a.state&&(a.state="completed"):(s.component=1,o.candidate.candidate=n.writeCandidate(s));var d=n.splitSections(i.localDescription.sdp);d[o.candidate.sdpMLineIndex+1]+=c?"a=end-of-candidates\r\n":"a="+o.candidate.candidate+"\r\n",i.localDescription.sdp=d.join("");var p=(i._pendingOffer?i._pendingOffer:i.transceivers).every((function(e){return e.iceGatherer&&"completed"===e.iceGatherer.state}));switch(i.iceGatheringState){case"new":c||i._localIceCandidatesBuffer.push(o),c&&p&&i._localIceCandidatesBuffer.push(new Event("icecandidate"));break;case"gathering":i._emitBufferedCandidates(),c||(i.dispatchEvent(o),null!==i.onicecandidate&&i.onicecandidate(o)),p&&(i.dispatchEvent(new Event("icecandidate")),null!==i.onicecandidate&&i.onicecandidate(new Event("icecandidate")),i.iceGatheringState="complete",i._emitGatheringStateChange())}},a},r.prototype._createIceAndDtlsTransports=function(){var t=this,r=new e.RTCIceTransport(null);r.onicestatechange=function(){t._updateConnectionState()};var n=new e.RTCDtlsTransport(r);return n.ondtlsstatechange=function(){t._updateConnectionState()},n.onerror=function(){Object.defineProperty(n,"state",{value:"failed",writable:!0}),t._updateConnectionState()},{iceTransport:r,dtlsTransport:n}},r.prototype._disposeIceAndDtlsTransports=function(e){var t=this.transceivers[e].iceGatherer;t&&(delete t.onlocalcandidate,delete this.transceivers[e].iceGatherer);var r=this.transceivers[e].iceTransport;r&&(delete r.onicestatechange,delete this.transceivers[e].iceTransport);var n=this.transceivers[e].dtlsTransport;n&&(delete n.ondtlsstatechange,delete n.onerror,delete this.transceivers[e].dtlsTransport)},r.prototype._transceive=function(e,r,a){var o=i(e.localCapabilities,e.remoteCapabilities);r&&e.rtpSender&&(o.encodings=e.sendEncodingParameters,o.rtcp={cname:n.localCName,compound:e.rtcpParameters.compound},e.recvEncodingParameters.length&&(o.rtcp.ssrc=e.recvEncodingParameters[0].ssrc),e.rtpSender.send(o)),a&&e.rtpReceiver&&("video"===e.kind&&e.recvEncodingParameters&&t<15019&&e.recvEncodingParameters.forEach((function(e){delete e.rtx})),o.encodings=e.recvEncodingParameters,o.rtcp={cname:e.rtcpParameters.cname,compound:e.rtcpParameters.compound},e.sendEncodingParameters.length&&(o.rtcp.ssrc=e.sendEncodingParameters[0].ssrc),e.rtpReceiver.receive(o))},r.prototype.setLocalDescription=function(t){var r,o,s=this;if(!a("setLocalDescription",t.type,this.signalingState)){var c=new Error("Can not set local "+t.type+" in state "+this.signalingState);return c.name="InvalidStateError",arguments.length>2&&"function"==typeof arguments[2]&&e.setTimeout(arguments[2],0,c),Promise.reject(c)}if("offer"===t.type)this._pendingOffer&&(r=n.splitSections(t.sdp),o=r.shift(),r.forEach((function(e,t){var r=n.parseRtpParameters(e);s._pendingOffer[t].localCapabilities=r})),this.transceivers=this._pendingOffer,delete this._pendingOffer);else if("answer"===t.type){r=n.splitSections(s.remoteDescription.sdp),o=r.shift();var d=n.matchPrefix(o,"a=ice-lite").length>0;r.forEach((function(e,t){var r=s.transceivers[t],a=r.iceGatherer,c=r.iceTransport,p=r.dtlsTransport,u=r.localCapabilities,l=r.remoteCapabilities;if(!n.isRejected(e)&&!r.isDatachannel){var f=n.getIceParameters(e,o),m=n.getDtlsParameters(e,o);d&&(m.role="server"),s.usingBundle&&0!==t||(c.start(a,f,d?"controlling":"controlled"),p.start(m));var h=i(u,l);s._transceive(r,h.codecs.length>0,!1)}}))}switch(this.localDescription={type:t.type,sdp:t.sdp},t.type){case"offer":this._updateSignalingState("have-local-offer");break;case"answer":this._updateSignalingState("stable");break;default:throw new TypeError('unsupported type "'+t.type+'"')}var p=arguments.length>1&&"function"==typeof arguments[1];if(p){var u=arguments[1];e.setTimeout((function(){u(),"new"===s.iceGatheringState&&(s.iceGatheringState="gathering",s._emitGatheringStateChange()),s._emitBufferedCandidates()}),0)}var l=Promise.resolve();return l.then((function(){p||("new"===s.iceGatheringState&&(s.iceGatheringState="gathering",s._emitGatheringStateChange()),e.setTimeout(s._emitBufferedCandidates.bind(s),500))})),l},r.prototype.setRemoteDescription=function(r){var i=this;if(!a("setRemoteDescription",r.type,this.signalingState)){var o=new Error("Can not set remote "+r.type+" in state "+this.signalingState);return o.name="InvalidStateError",arguments.length>2&&"function"==typeof arguments[2]&&e.setTimeout(arguments[2],0,o),Promise.reject(o)}var s={},c=[],d=n.splitSections(r.sdp),p=d.shift(),u=n.matchPrefix(p,"a=ice-lite").length>0,l=n.matchPrefix(p,"a=group:BUNDLE ").length>0;this.usingBundle=l;var f=n.matchPrefix(p,"a=ice-options:")[0];switch(this.canTrickleIceCandidates=!!f&&f.substr(14).split(" ").indexOf("trickle")>=0,d.forEach((function(a,o){var d=n.splitLines(a),f=n.getKind(a),m=n.isRejected(a),h=d[0].substr(2).split(" ")[2],v=n.getDirection(a,p),g=n.parseMsid(a),y=n.getMid(a)||n.generateIdentifier();if("application"!==f||"DTLS/SCTP"!==h){var C,T,S,P,b,R,k,w,E,x,O,_=n.parseRtpParameters(a);m||(x=n.getIceParameters(a,p),(O=n.getDtlsParameters(a,p)).role="client"),k=n.parseRtpEncodingParameters(a);var D=n.parseRtcpParameters(a),M=n.matchPrefix(a,"a=end-of-candidates",p).length>0,I=n.matchPrefix(a,"a=candidate:").map((function(e){return n.parseCandidate(e)})).filter((function(e){return"1"===e.component||1===e.component}));("offer"===r.type||"answer"===r.type)&&!m&&l&&o>0&&i.transceivers[o]&&(i._disposeIceAndDtlsTransports(o),i.transceivers[o].iceGatherer=i.transceivers[0].iceGatherer,i.transceivers[o].iceTransport=i.transceivers[0].iceTransport,i.transceivers[o].dtlsTransport=i.transceivers[0].dtlsTransport,i.transceivers[o].rtpSender&&i.transceivers[o].rtpSender.setTransport(i.transceivers[0].dtlsTransport),i.transceivers[o].rtpReceiver&&i.transceivers[o].rtpReceiver.setTransport(i.transceivers[0].dtlsTransport)),"offer"!==r.type||m?"answer"!==r.type||m||(T=(C=i.transceivers[o]).iceGatherer,S=C.iceTransport,P=C.dtlsTransport,b=C.rtpReceiver,R=C.sendEncodingParameters,w=C.localCapabilities,i.transceivers[o].recvEncodingParameters=k,i.transceivers[o].remoteCapabilities=_,i.transceivers[o].rtcpParameters=D,l&&0!==o||((u||M)&&I.length&&S.setRemoteCandidates(I),S.start(T,x,"controlling"),P.start(O)),i._transceive(C,"sendrecv"===v||"recvonly"===v,"sendrecv"===v||"sendonly"===v),!b||"sendrecv"!==v&&"sendonly"!==v?delete C.rtpReceiver:(E=b.track,g?(s[g.stream]||(s[g.stream]=new e.MediaStream),s[g.stream].addTrack(E),c.push([E,b,s[g.stream]])):(s.default||(s.default=new e.MediaStream),s.default.addTrack(E),c.push([E,b,s.default])))):((C=i.transceivers[o]||i._createTransceiver(f)).mid=y,C.iceGatherer||(C.iceGatherer=l&&o>0?i.transceivers[0].iceGatherer:i._createIceGatherer(y,o)),!M||!I.length||l&&0!==o||C.iceTransport.setRemoteCandidates(I),w=e.RTCRtpReceiver.getCapabilities(f),t<15019&&(w.codecs=w.codecs.filter((function(e){return"rtx"!==e.name}))),R=[{ssrc:1001*(2*o+2)}],"sendrecv"!==v&&"sendonly"!==v||(E=(b=new e.RTCRtpReceiver(C.dtlsTransport,f)).track,g?(s[g.stream]||(s[g.stream]=new e.MediaStream,Object.defineProperty(s[g.stream],"id",{get:function(){return g.stream}})),Object.defineProperty(E,"id",{get:function(){return g.track}}),s[g.stream].addTrack(E),c.push([E,b,s[g.stream]])):(s.default||(s.default=new e.MediaStream),s.default.addTrack(E),c.push([E,b,s.default]))),C.localCapabilities=w,C.remoteCapabilities=_,C.rtpReceiver=b,C.rtcpParameters=D,C.sendEncodingParameters=R,C.recvEncodingParameters=k,i._transceive(i.transceivers[o],!1,"sendrecv"===v||"sendonly"===v))}else i.transceivers[o]={mid:y,isDatachannel:!0}})),this.remoteDescription={type:r.type,sdp:r.sdp},r.type){case"offer":this._updateSignalingState("have-remote-offer");break;case"answer":this._updateSignalingState("stable");break;default:throw new TypeError('unsupported type "'+r.type+'"')}return Object.keys(s).forEach((function(t){var r=s[t];if(r.getTracks().length){i.remoteStreams.push(r);var n=new Event("addstream");n.stream=r,i.dispatchEvent(n),null!==i.onaddstream&&e.setTimeout((function(){i.onaddstream(n)}),0),c.forEach((function(t){var n=t[0],a=t[1];if(r.id===t[2].id){var o=new Event("track");o.track=n,o.receiver=a,o.streams=[r],i.dispatchEvent(o),null!==i.ontrack&&e.setTimeout((function(){i.ontrack(o)}),0)}}))}})),e.setTimeout((function(){i&&i.transceivers&&i.transceivers.forEach((function(e){e.iceTransport&&"new"===e.iceTransport.state&&e.iceTransport.getRemoteCandidates().length>0&&(console.warn("Timeout for addRemoteCandidate. Consider sending an end-of-candidates notification"),e.iceTransport.addRemoteCandidate({}))}))}),4e3),arguments.length>1&&"function"==typeof arguments[1]&&e.setTimeout(arguments[1],0),Promise.resolve()},r.prototype.close=function(){this.transceivers.forEach((function(e){e.iceTransport&&e.iceTransport.stop(),e.dtlsTransport&&e.dtlsTransport.stop(),e.rtpSender&&e.rtpSender.stop(),e.rtpReceiver&&e.rtpReceiver.stop()})),this._updateSignalingState("closed")},r.prototype._updateSignalingState=function(e){this.signalingState=e;var t=new Event("signalingstatechange");this.dispatchEvent(t),null!==this.onsignalingstatechange&&this.onsignalingstatechange(t)},r.prototype._maybeFireNegotiationNeeded=function(){var t=this;"stable"===this.signalingState&&!0!==this.needNegotiation&&(this.needNegotiation=!0,e.setTimeout((function(){if(!1!==t.needNegotiation){t.needNegotiation=!1;var e=new Event("negotiationneeded");t.dispatchEvent(e),null!==t.onnegotiationneeded&&t.onnegotiationneeded(e)}}),0))},r.prototype._updateConnectionState=function(){var e,t={new:0,closed:0,connecting:0,checking:0,connected:0,completed:0,disconnected:0,failed:0};if(this.transceivers.forEach((function(e){t[e.iceTransport.state]++,t[e.dtlsTransport.state]++})),t.connected+=t.completed,e="new",t.failed>0?e="failed":t.connecting>0||t.checking>0?e="connecting":t.disconnected>0?e="disconnected":t.new>0?e="new":(t.connected>0||t.completed>0)&&(e="connected"),e!==this.iceConnectionState){this.iceConnectionState=e;var r=new Event("iceconnectionstatechange");this.dispatchEvent(r),null!==this.oniceconnectionstatechange&&this.oniceconnectionstatechange(r)}},r.prototype.createOffer=function(){var r,i=this;if(this._pendingOffer)throw new Error("createOffer called while there is a pending offer.");1===arguments.length&&"function"!=typeof arguments[0]?r=arguments[0]:3===arguments.length&&(r=arguments[2]);var a=this.transceivers.filter((function(e){return"audio"===e.kind})).length,o=this.transceivers.filter((function(e){return"video"===e.kind})).length;if(r){if(r.mandatory||r.optional)throw new TypeError("Legacy mandatory/optional constraints not supported.");void 0!==r.offerToReceiveAudio&&(a=!0===r.offerToReceiveAudio?1:!1===r.offerToReceiveAudio?0:r.offerToReceiveAudio),void 0!==r.offerToReceiveVideo&&(o=!0===r.offerToReceiveVideo?1:!1===r.offerToReceiveVideo?0:r.offerToReceiveVideo)}for(this.transceivers.forEach((function(e){"audio"===e.kind?--a<0&&(e.wantReceive=!1):"video"===e.kind&&--o<0&&(e.wantReceive=!1)}));a>0||o>0;)a>0&&(this._createTransceiver("audio"),a--),o>0&&(this._createTransceiver("video"),o--);var s=function(e){var t=e.filter((function(e){return"audio"===e.kind})),r=e.filter((function(e){return"video"===e.kind}));for(e=[];t.length||r.length;)t.length&&e.push(t.shift()),r.length&&e.push(r.shift());return e}(this.transceivers),c=n.writeSessionBoilerplate(this._sdpSessionId);s.forEach((function(r,a){var o=r.track,c=r.kind,d=n.generateIdentifier();r.mid=d,r.iceGatherer||(r.iceGatherer=i.usingBundle&&a>0?s[0].iceGatherer:i._createIceGatherer(d,a));var p=e.RTCRtpSender.getCapabilities(c);t<15019&&(p.codecs=p.codecs.filter((function(e){return"rtx"!==e.name}))),p.codecs.forEach((function(e){"H264"===e.name&&void 0===e.parameters["level-asymmetry-allowed"]&&(e.parameters["level-asymmetry-allowed"]="1")}));var u=[{ssrc:1001*(2*a+1)}];o&&t>=15019&&"video"===c&&(u[0].rtx={ssrc:1001*(2*a+1)+1}),r.wantReceive&&(r.rtpReceiver=new e.RTCRtpReceiver(r.dtlsTransport,c)),r.localCapabilities=p,r.sendEncodingParameters=u})),"max-compat"!==this._config.bundlePolicy&&(c+="a=group:BUNDLE "+s.map((function(e){return e.mid})).join(" ")+"\r\n"),c+="a=ice-options:trickle\r\n",s.forEach((function(e,t){c+=n.writeMediaSection(e,e.localCapabilities,"offer",e.stream),c+="a=rtcp-rsize\r\n"})),this._pendingOffer=s;var d=new e.RTCSessionDescription({type:"offer",sdp:c});return arguments.length&&"function"==typeof arguments[0]&&e.setTimeout(arguments[0],0,d),Promise.resolve(d)},r.prototype.createAnswer=function(){var r=n.writeSessionBoilerplate(this._sdpSessionId);this.usingBundle&&(r+="a=group:BUNDLE "+this.transceivers.map((function(e){return e.mid})).join(" ")+"\r\n"),this.transceivers.forEach((function(e,a){if(e.isDatachannel)r+="m=application 0 DTLS/SCTP 5000\r\nc=IN IP4 0.0.0.0\r\na=mid:"+e.mid+"\r\n";else{var o;e.stream&&("audio"===e.kind?o=e.stream.getAudioTracks()[0]:"video"===e.kind&&(o=e.stream.getVideoTracks()[0]),o&&t>=15019&&"video"===e.kind&&(e.sendEncodingParameters[0].rtx={ssrc:1001*(2*a+2)+1}));var s=i(e.localCapabilities,e.remoteCapabilities);!s.codecs.filter((function(e){return"rtx"===e.name.toLowerCase()})).length&&e.sendEncodingParameters[0].rtx&&delete e.sendEncodingParameters[0].rtx,r+=n.writeMediaSection(e,s,"answer",e.stream),e.rtcpParameters&&e.rtcpParameters.reducedSize&&(r+="a=rtcp-rsize\r\n")}}));var a=new e.RTCSessionDescription({type:"answer",sdp:r});return arguments.length&&"function"==typeof arguments[0]&&e.setTimeout(arguments[0],0,a),Promise.resolve(a)},r.prototype.addIceCandidate=function(t){if(t){var r=t.sdpMLineIndex;if(t.sdpMid)for(var i=0;i<this.transceivers.length;i++)if(this.transceivers[i].mid===t.sdpMid){r=i;break}var a=this.transceivers[r];if(a){var o=Object.keys(t.candidate).length>0?n.parseCandidate(t.candidate):{};if("tcp"===o.protocol&&(0===o.port||9===o.port))return Promise.resolve();if(o.component&&"1"!==o.component&&1!==o.component)return Promise.resolve();a.iceTransport.addRemoteCandidate(o);var s=n.splitSections(this.remoteDescription.sdp);s[r+1]+=(o.type?t.candidate.trim():"a=end-of-candidates")+"\r\n",this.remoteDescription.sdp=s.join("")}}else for(var c=0;c<this.transceivers.length;c++)if(this.transceivers[c].iceTransport.addRemoteCandidate({}),this.usingBundle)return Promise.resolve();return arguments.length>1&&"function"==typeof arguments[1]&&e.setTimeout(arguments[1],0),Promise.resolve()},r.prototype.getStats=function(){var t=[];this.transceivers.forEach((function(e){["rtpSender","rtpReceiver","iceGatherer","iceTransport","dtlsTransport"].forEach((function(r){e[r]&&t.push(e[r].getStats())}))}));var r=arguments.length>1&&"function"==typeof arguments[1]&&arguments[1];return new Promise((function(n){var i=new Map;Promise.all(t).then((function(t){t.forEach((function(e){Object.keys(e).forEach((function(t){var r;e[t].type={inboundrtp:"inbound-rtp",outboundrtp:"outbound-rtp",candidatepair:"candidate-pair",localcandidate:"local-candidate",remotecandidate:"remote-candidate"}[(r=e[t]).type]||r.type,i.set(t,e[t])}))})),r&&e.setTimeout(r,0,i),n(i)}))}))},r}},542:(e,t,r)=>{var n=r(634),i={shimOnTrack:function(e){"object"==typeof e&&e.RTCPeerConnection&&!("ontrack"in e.RTCPeerConnection.prototype)&&Object.defineProperty(e.RTCPeerConnection.prototype,"ontrack",{get:function(){return this._ontrack},set:function(e){this._ontrack&&(this.removeEventListener("track",this._ontrack),this.removeEventListener("addstream",this._ontrackpoly)),this.addEventListener("track",this._ontrack=e),this.addEventListener("addstream",this._ontrackpoly=function(e){e.stream.getTracks().forEach(function(t){var r=new Event("track");r.track=t,r.receiver={track:t},r.streams=[e.stream],this.dispatchEvent(r)}.bind(this))}.bind(this))}})},shimSourceObject:function(e){"object"==typeof e&&e.HTMLMediaElement&&!("srcObject"in e.HTMLMediaElement.prototype)&&Object.defineProperty(e.HTMLMediaElement.prototype,"srcObject",{get:function(){return this.mozSrcObject},set:function(e){this.mozSrcObject=e}})},shimPeerConnection:function(e){var t=n.detectBrowser(e);if("object"==typeof e&&(e.RTCPeerConnection||e.mozRTCPeerConnection)){e.RTCPeerConnection||(e.RTCPeerConnection=function(r,n){if(t.version<38&&r&&r.iceServers){for(var i=[],a=0;a<r.iceServers.length;a++){var o=r.iceServers[a];if(o.hasOwnProperty("urls"))for(var s=0;s<o.urls.length;s++){var c={url:o.urls[s]};0===o.urls[s].indexOf("turn")&&(c.username=o.username,c.credential=o.credential),i.push(c)}else i.push(r.iceServers[a])}r.iceServers=i}return new e.mozRTCPeerConnection(r,n)},e.RTCPeerConnection.prototype=e.mozRTCPeerConnection.prototype,e.mozRTCPeerConnection.generateCertificate&&Object.defineProperty(e.RTCPeerConnection,"generateCertificate",{get:function(){return e.mozRTCPeerConnection.generateCertificate}}),e.RTCSessionDescription=e.mozRTCSessionDescription,e.RTCIceCandidate=e.mozRTCIceCandidate),["setLocalDescription","setRemoteDescription","addIceCandidate"].forEach((function(t){var r=e.RTCPeerConnection.prototype[t];e.RTCPeerConnection.prototype[t]=function(){return arguments[0]=new("addIceCandidate"===t?e.RTCIceCandidate:e.RTCSessionDescription)(arguments[0]),r.apply(this,arguments)}}));var r=e.RTCPeerConnection.prototype.addIceCandidate;e.RTCPeerConnection.prototype.addIceCandidate=function(){return arguments[0]?r.apply(this,arguments):(arguments[1]&&arguments[1].apply(null),Promise.resolve())};var i={inboundrtp:"inbound-rtp",outboundrtp:"outbound-rtp",candidatepair:"candidate-pair",localcandidate:"local-candidate",remotecandidate:"remote-candidate"},a=e.RTCPeerConnection.prototype.getStats;e.RTCPeerConnection.prototype.getStats=function(e,r,n){return a.apply(this,[e||null]).then((function(e){if(t.version<48&&(e=function(e){var t=new Map;return Object.keys(e).forEach((function(r){t.set(r,e[r]),t[r]=e[r]})),t}(e)),t.version<53&&!r)try{e.forEach((function(e){e.type=i[e.type]||e.type}))}catch(t){if("TypeError"!==t.name)throw t;e.forEach((function(t,r){e.set(r,Object.assign({},t,{type:i[t.type]||t.type}))}))}return e})).then(r,n)}}}};e.exports={shimOnTrack:i.shimOnTrack,shimSourceObject:i.shimSourceObject,shimPeerConnection:i.shimPeerConnection,shimGetUserMedia:r(176)}},176:(e,t,r)=>{var n=r(634),i=n.log;e.exports=function(e){var t=n.detectBrowser(e),r=e&&e.navigator,a=e&&e.MediaStreamTrack,o=function(e){return{name:{InternalError:"NotReadableError",NotSupportedError:"TypeError",PermissionDeniedError:"NotAllowedError",SecurityError:"NotAllowedError"}[e.name]||e.name,message:{"The operation is insecure.":"The request is not allowed by the user agent or the platform in the current context."}[e.message]||e.message,constraint:e.constraint,toString:function(){return this.name+(this.message&&": ")+this.message}}},s=function(e,n,a){var s=function(e){if("object"!=typeof e||e.require)return e;var t=[];return Object.keys(e).forEach((function(r){if("require"!==r&&"advanced"!==r&&"mediaSource"!==r){var n=e[r]="object"==typeof e[r]?e[r]:{ideal:e[r]};if(void 0===n.min&&void 0===n.max&&void 0===n.exact||t.push(r),void 0!==n.exact&&("number"==typeof n.exact?n.min=n.max=n.exact:e[r]=n.exact,delete n.exact),void 0!==n.ideal){e.advanced=e.advanced||[];var i={};"number"==typeof n.ideal?i[r]={min:n.ideal,max:n.ideal}:i[r]=n.ideal,e.advanced.push(i),delete n.ideal,Object.keys(n).length||delete e[r]}}})),t.length&&(e.require=t),e};return e=JSON.parse(JSON.stringify(e)),t.version<38&&(i("spec: "+JSON.stringify(e)),e.audio&&(e.audio=s(e.audio)),e.video&&(e.video=s(e.video)),i("ff37: "+JSON.stringify(e))),r.mozGetUserMedia(e,n,(function(e){a(o(e))}))};if(r.mediaDevices||(r.mediaDevices={getUserMedia:function(e){return new Promise((function(t,r){s(e,t,r)}))},addEventListener:function(){},removeEventListener:function(){}}),r.mediaDevices.enumerateDevices=r.mediaDevices.enumerateDevices||function(){return new Promise((function(e){e([{kind:"audioinput",deviceId:"default",label:"",groupId:""},{kind:"videoinput",deviceId:"default",label:"",groupId:""}])}))},t.version<41){var c=r.mediaDevices.enumerateDevices.bind(r.mediaDevices);r.mediaDevices.enumerateDevices=function(){return c().then(void 0,(function(e){if("NotFoundError"===e.name)return[];throw e}))}}if(t.version<49){var d=r.mediaDevices.getUserMedia.bind(r.mediaDevices);r.mediaDevices.getUserMedia=function(e){return d(e).then((function(t){if(e.audio&&!t.getAudioTracks().length||e.video&&!t.getVideoTracks().length)throw t.getTracks().forEach((function(e){e.stop()})),new DOMException("The object can not be found here.","NotFoundError");return t}),(function(e){return Promise.reject(o(e))}))}}if(!(t.version>55&&"autoGainControl"in r.mediaDevices.getSupportedConstraints())){var p=function(e,t,r){t in e&&!(r in e)&&(e[r]=e[t],delete e[t])},u=r.mediaDevices.getUserMedia.bind(r.mediaDevices);if(r.mediaDevices.getUserMedia=function(e){return"object"==typeof e&&"object"==typeof e.audio&&(e=JSON.parse(JSON.stringify(e)),p(e.audio,"autoGainControl","mozAutoGainControl"),p(e.audio,"noiseSuppression","mozNoiseSuppression")),u(e)},a&&a.prototype.getSettings){var l=a.prototype.getSettings;a.prototype.getSettings=function(){var e=l.apply(this,arguments);return p(e,"mozAutoGainControl","autoGainControl"),p(e,"mozNoiseSuppression","noiseSuppression"),e}}if(a&&a.prototype.applyConstraints){var f=a.prototype.applyConstraints;a.prototype.applyConstraints=function(e){return"audio"===this.kind&&"object"==typeof e&&(e=JSON.parse(JSON.stringify(e)),p(e,"autoGainControl","mozAutoGainControl"),p(e,"noiseSuppression","mozNoiseSuppression")),f.apply(this,[e])}}}r.getUserMedia=function(e,i,a){if(t.version<44)return s(e,i,a);n.deprecated("navigator.getUserMedia","navigator.mediaDevices.getUserMedia"),r.mediaDevices.getUserMedia(e).then(i,a)}}},824:(e,t,r)=>{var n=r(634),i={shimLocalStreamsAPI:function(e){if("object"==typeof e&&e.RTCPeerConnection){if("getLocalStreams"in e.RTCPeerConnection.prototype||(e.RTCPeerConnection.prototype.getLocalStreams=function(){return this._localStreams||(this._localStreams=[]),this._localStreams}),"getStreamById"in e.RTCPeerConnection.prototype||(e.RTCPeerConnection.prototype.getStreamById=function(e){var t=null;return this._localStreams&&this._localStreams.forEach((function(r){r.id===e&&(t=r)})),this._remoteStreams&&this._remoteStreams.forEach((function(r){r.id===e&&(t=r)})),t}),!("addStream"in e.RTCPeerConnection.prototype)){var t=e.RTCPeerConnection.prototype.addTrack;e.RTCPeerConnection.prototype.addStream=function(e){this._localStreams||(this._localStreams=[]),-1===this._localStreams.indexOf(e)&&this._localStreams.push(e);var r=this;e.getTracks().forEach((function(n){t.call(r,n,e)}))},e.RTCPeerConnection.prototype.addTrack=function(e,r){r&&(this._localStreams?-1===this._localStreams.indexOf(r)&&this._localStreams.push(r):this._localStreams=[r]),t.call(this,e,r)}}"removeStream"in e.RTCPeerConnection.prototype||(e.RTCPeerConnection.prototype.removeStream=function(e){this._localStreams||(this._localStreams=[]);var t=this._localStreams.indexOf(e);if(-1!==t){this._localStreams.splice(t,1);var r=this,n=e.getTracks();this.getSenders().forEach((function(e){-1!==n.indexOf(e.track)&&r.removeTrack(e)}))}})}},shimRemoteStreamsAPI:function(e){"object"==typeof e&&e.RTCPeerConnection&&("getRemoteStreams"in e.RTCPeerConnection.prototype||(e.RTCPeerConnection.prototype.getRemoteStreams=function(){return this._remoteStreams?this._remoteStreams:[]}),"onaddstream"in e.RTCPeerConnection.prototype||Object.defineProperty(e.RTCPeerConnection.prototype,"onaddstream",{get:function(){return this._onaddstream},set:function(e){this._onaddstream&&(this.removeEventListener("addstream",this._onaddstream),this.removeEventListener("track",this._onaddstreampoly)),this.addEventListener("addstream",this._onaddstream=e),this.addEventListener("track",this._onaddstreampoly=function(e){var t=e.streams[0];if(this._remoteStreams||(this._remoteStreams=[]),!(this._remoteStreams.indexOf(t)>=0)){this._remoteStreams.push(t);var r=new Event("addstream");r.stream=e.streams[0],this.dispatchEvent(r)}}.bind(this))}}))},shimCallbacksAPI:function(e){if("object"==typeof e&&e.RTCPeerConnection){var t=e.RTCPeerConnection.prototype,r=t.createOffer,n=t.createAnswer,i=t.setLocalDescription,a=t.setRemoteDescription,o=t.addIceCandidate;t.createOffer=function(e,t){var n=arguments.length>=2?arguments[2]:arguments[0],i=r.apply(this,[n]);return t?(i.then(e,t),Promise.resolve()):i},t.createAnswer=function(e,t){var r=arguments.length>=2?arguments[2]:arguments[0],i=n.apply(this,[r]);return t?(i.then(e,t),Promise.resolve()):i};var s=function(e,t,r){var n=i.apply(this,[e]);return r?(n.then(t,r),Promise.resolve()):n};t.setLocalDescription=s,s=function(e,t,r){var n=a.apply(this,[e]);return r?(n.then(t,r),Promise.resolve()):n},t.setRemoteDescription=s,s=function(e,t,r){var n=o.apply(this,[e]);return r?(n.then(t,r),Promise.resolve()):n},t.addIceCandidate=s}},shimGetUserMedia:function(e){var t=e&&e.navigator;t.getUserMedia||(t.webkitGetUserMedia?t.getUserMedia=t.webkitGetUserMedia.bind(t):t.mediaDevices&&t.mediaDevices.getUserMedia&&(t.getUserMedia=function(e,r,n){t.mediaDevices.getUserMedia(e).then(r,n)}.bind(t)))},shimRTCIceServerUrls:function(e){var t=e.RTCPeerConnection;e.RTCPeerConnection=function(e,r){if(e&&e.iceServers){for(var i=[],a=0;a<e.iceServers.length;a++){var o=e.iceServers[a];!o.hasOwnProperty("urls")&&o.hasOwnProperty("url")?(n.deprecated("RTCIceServer.url","RTCIceServer.urls"),(o=JSON.parse(JSON.stringify(o))).urls=o.url,delete o.url,i.push(o)):i.push(e.iceServers[a])}e.iceServers=i}return new t(e,r)},e.RTCPeerConnection.prototype=t.prototype,Object.defineProperty(e.RTCPeerConnection,"generateCertificate",{get:function(){return t.generateCertificate}})}};e.exports={shimCallbacksAPI:i.shimCallbacksAPI,shimLocalStreamsAPI:i.shimLocalStreamsAPI,shimRemoteStreamsAPI:i.shimRemoteStreamsAPI,shimGetUserMedia:i.shimGetUserMedia,shimRTCIceServerUrls:i.shimRTCIceServerUrls}},634:e=>{var t=!0,r=!0,n={disableLog:function(e){return"boolean"!=typeof e?new Error("Argument type: "+typeof e+". Please use a boolean."):(t=e,e?"adapter.js logging disabled":"adapter.js logging enabled")},disableWarnings:function(e){return"boolean"!=typeof e?new Error("Argument type: "+typeof e+". Please use a boolean."):(r=!e,"adapter.js deprecation warnings "+(e?"disabled":"enabled"))},log:function(){if("object"==typeof window){if(t)return;"undefined"!=typeof console&&"function"==typeof console.log&&console.log.apply(console,arguments)}},deprecated:function(e,t){r&&console.warn(e+" is deprecated, please use "+t+" instead.")},extractVersion:function(e,t,r){var n=e.match(t);return n&&n.length>=r&&parseInt(n[r],10)},detectBrowser:function(e){var t=e&&e.navigator,r={browser:null,version:null};if(void 0===e||!e.navigator)return r.browser="Not a browser.",r;if(t.mozGetUserMedia)r.browser="firefox",r.version=this.extractVersion(t.userAgent,/Firefox\/(\d+)\./,1);else if(t.webkitGetUserMedia)if(e.webkitRTCPeerConnection)r.browser="chrome",r.version=this.extractVersion(t.userAgent,/Chrom(e|ium)\/(\d+)\./,2);else{if(!t.userAgent.match(/Version\/(\d+).(\d+)/))return r.browser="Unsupported webkit-based browser with GUM support but no WebRTC support.",r;r.browser="safari",r.version=this.extractVersion(t.userAgent,/AppleWebKit\/(\d+)\./,1)}else if(t.mediaDevices&&t.userAgent.match(/Edge\/(\d+).(\d+)$/))r.browser="edge",r.version=this.extractVersion(t.userAgent,/Edge\/(\d+).(\d+)$/,2);else{if(!t.mediaDevices||!t.userAgent.match(/AppleWebKit\/(\d+)\./))return r.browser="Not a supported browser.",r;r.browser="safari",r.version=this.extractVersion(t.userAgent,/AppleWebKit\/(\d+)\./,1)}return r},shimCreateObjectURL:function(e){var t=e&&e.URL;if("object"==typeof e&&e.HTMLMediaElement&&"srcObject"in e.HTMLMediaElement.prototype){var r=t.createObjectURL.bind(t),i=t.revokeObjectURL.bind(t),a=new Map,o=0;t.createObjectURL=function(e){if("getTracks"in e){var t="polyblob:"+ ++o;return a.set(t,e),n.deprecated("URL.createObjectURL(stream)","elem.srcObject = stream"),t}return r(e)},t.revokeObjectURL=function(e){i(e),a.delete(e)};var s=Object.getOwnPropertyDescriptor(e.HTMLMediaElement.prototype,"src");Object.defineProperty(e.HTMLMediaElement.prototype,"src",{get:function(){return s.get.apply(this)},set:function(e){return this.srcObject=a.get(e)||null,s.set.apply(this,[e])}});var c=e.HTMLMediaElement.prototype.setAttribute;e.HTMLMediaElement.prototype.setAttribute=function(){return 2===arguments.length&&"src"===(""+arguments[0]).toLowerCase()&&(this.srcObject=a.get(arguments[1])||null),c.apply(this,arguments)}}}};e.exports={log:n.log,deprecated:n.deprecated,disableLog:n.disableLog,disableWarnings:n.disableWarnings,extractVersion:n.extractVersion,shimCreateObjectURL:n.shimCreateObjectURL,detectBrowser:n.detectBrowser.bind(n)}}}]);