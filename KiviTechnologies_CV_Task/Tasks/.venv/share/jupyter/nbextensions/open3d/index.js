/*! For license information please see index.js.LICENSE.txt */
define(["module","@jupyter-widgets/base"],((e,t)=>(()=>{var n={925:(e,t,n)=>{var r=n(308),i=new URL(r.uri,document.location);i.pathname=i.pathname.slice(0,i.pathname.lastIndexOf("/")+1),n.p=`${i.origin}${i.pathname}`},509:(e,t,n)=>{e.exports=n(282),e.exports.version=n(330).version},282:(e,t,n)=>{let r=n(55),i=n(543);n(752);let o=n(155);class a extends r.DOMWidgetModel{defaults(){return i.extend(r.DOMWidgetModel.prototype.defaults(),{_model_name:"WebVisualizerModel",_view_name:"WebVisualizerView",_model_module:"open3d",_view_module:"open3d",_model_module_version:"0.19.0",_view_module_version:"0.19.0"})}}class s extends r.DOMWidgetView{sleep(e){return new Promise((t=>setTimeout(t,e)))}logAndReturn(e){return console.log("logAndReturn: ",e),e}callResultReady(e){let t=this.model.get("pyjs_channel");return console.log("Current pyjs_channel:",t),e in JSON.parse(this.model.get("pyjs_channel"))}extractCallResult(e){if(!this.callResultReady(e))throw"extractCallResult not ready yet.";return JSON.parse(this.model.get("pyjs_channel"))[e]}async callPython(e,t=[]){let n=this.callId.toString();this.callId++;let r={func:e,args:t,call_id:n},i=this.model.get("jspy_channel"),o=JSON.parse(i);o[n]=r,i=JSON.stringify(o),this.model.set("jspy_channel",i),this.touch();let a=0;for(;!this.callResultReady(n);)console.log("callPython await, id: "+n+", count: "+a++),await this.sleep(100);let s=this.extractCallResult(n);return console.log("callPython await done, id:",n,"json_result:",s),s}commsCall(e,t={}){let n=function(e){let t=document.createElement("a");return t.href=e,t},r=n(e).pathname;if(["/api/getMediaList","/api/getIceServers","/api/hangup","/api/call","/api/getIceCandidate","/api/addIceCandidate"].indexOf(r)>=0){let i=n(e).search;i||(i="");let o=t.body;return o||(o=""),console.log("WebVisualizerView.commsCall with url: ",e," data: ",t),console.log("WebVisualizerView.commsCall with entryPoint: ",r),console.log("WebVisualizerView.commsCall with queryString: ",i),console.log('WebVisualizerView.commsCall with data["body"]: ',o),this.callPython("call_http_api",[r,i,o]).then((e=>JSON.parse(e))).then((e=>this.logAndReturn(e))).then((e=>new Response(new Blob([JSON.stringify(e)],{type:"application/json"})))).then((e=>this.logAndReturn(e)))}throw"Unsupported entryPoint: "+r}render(){let e=this.model.get("window_uid");console.log("Entered render() function."),this.model.set("pyjs_channel","{}"),this.model.set("jspy_channel","{}"),this.touch(),this.callId=0,this.videoElt=document.createElement("video"),this.videoElt.id="video_tag",this.videoElt.muted=!0,this.videoElt.controls=!1,this.videoElt.playsinline=!0,this.videoElt.innerText="Your browser does not support HTML5 video.",this.el.appendChild(this.videoElt),this.webRtcClient=new o(this.videoElt,"",(function(){console.log("onClose() called for window_uid:",e)}),this.commsCall.bind(this)),this.webRtcClient.connect(e)}}e.exports={WebVisualizerModel:a,WebVisualizerView:s}},155:e=>{void 0===window.console&&(window.console={}),window.console.log=window.console.info=window.console.debug=window.console.warning=window.console.assert=window.console.error=function(){};let t=function(){function e(e,t,n,r=null){this.videoElt="string"==typeof e?document.getElementById(e):e,this.srvurl=t||location.protocol+"//"+window.location.hostname+":"+window.location.port,this.pc=null,this.dataChannel=null,this.pcOptions={optional:[{DtlsSrtpKeyAgreement:!0}]},this.mediaConstraints={offerToReceiveAudio:!0,offerToReceiveVideo:!0},this.iceServers=null,this.earlyCandidates=[],this.onClose=n,this.commsFetch=r}return e.remoteCall=function(e,t={},n=null){return console.log("WebRtcStreamer.remoteCall{url: ",e,", data: ",t,", commsFetch",n,"}"),null==n?fetch(e,t):n(e,t)},e.getMediaList=function(t="",n=null){return e.remoteCall(t+"/api/getMediaList",{},n)},e._getModifiers=function(e){var t=2,n=8;window.navigator.platform.includes("Mac")&&([t,n]=[n,t]);var r=0;return e.getModifierState("Shift")&&(r|=1),e.getModifierState("Control")&&(r|=t),e.getModifierState("Alt")&&(r|=4),e.getModifierState("Meta")&&(r|=n),r},e.prototype._handleHttpErrors=function(e){if(!e.ok)throw Error(e.statusText);return e},e.prototype.connect=function(t,n,r,i){this.disconnect(),this.iceServers?this.onReceiveGetIceServers(this.iceServers,t,n,r,i):(console.log("Get IceServers"),e.remoteCall(this.srvurl+"/api/getIceServers",{},this.commsFetch).then(this._handleHttpErrors).then((e=>e.json())).then((e=>{return t=e,window.console.log("logAndReturn: ",t),t;var t})).then((e=>this.onReceiveGetIceServers.call(this,e,t,n,r,i))).catch((e=>this.onError("getIceServers "+e)))),this.addEventListeners(t)},e.prototype.sendJsonData=function(e){void 0!==this.dataChannel&&this.dataChannel.send(JSON.stringify(e))},e.prototype.addEventListeners=function(t){if(this.videoElt){this.videoElt.parentElement;var n=document.createElement("div"),r=document.createElement("input");r.id=t+"_height_input",r.type="text",r.value="",n.appendChild(r);var i=document.createElement("input");i.id=t+"_width_input",i.type="text",i.value="",n.appendChild(i);var o=document.createElement("button");o.id=t+"_resize_button",o.type="button",o.innerText="Resize",o.onclick=()=>{var e=document.getElementById(t+"_height_input"),n=document.getElementById(t+"_width_input");if(!e||!n)return void console.warn("Cannot resize, missing height/width inputs.");const r={window_uid:t,class_name:"ResizeEvent",height:parseInt(e.value),width:parseInt(n.value)};this.sendJsonData(r)},n.appendChild(o);var a=["LEFT","MIDDLE","RIGHT"];this.videoElt.addEventListener("contextmenu",(e=>{e.preventDefault()}),!1),this.videoElt.onloadedmetadata=function(){console.log("width is",this.videoWidth),console.log("height is",this.videoHeight);var e=document.getElementById(t+"_height_input");e&&(e.value=this.videoHeight);var n=document.getElementById(t+"_width_input");n&&(n.value=this.videoWidth)},this.videoElt.addEventListener("mousedown",(n=>{n.preventDefault();var r={window_uid:t,class_name:"MouseEvent",type:"BUTTON_DOWN",x:n.offsetX,y:n.offsetY,modifiers:e._getModifiers(n),button:{button:a[n.button],count:1}};this.sendJsonData(r)}),!1),this.videoElt.addEventListener("dblclick",(n=>{n.preventDefault();var r={window_uid:t,class_name:"MouseEvent",type:"BUTTON_DOWN",x:n.offsetX,y:n.offsetY,modifiers:e._getModifiers(n),button:{button:a[n.button],count:2}};this.sendJsonData(r)}),!1),this.videoElt.addEventListener("touchstart",(e=>{e.preventDefault();var n=e.target.getBoundingClientRect(),r={window_uid:t,class_name:"MouseEvent",type:"BUTTON_DOWN",x:Math.round(e.targetTouches[0].pageX-n.left),y:Math.round(e.targetTouches[0].pageY-n.top),modifiers:0,button:{button:a[e.button],count:1}};this.sendJsonData(r)}),!1),this.videoElt.addEventListener("mouseup",(n=>{n.preventDefault();var r={window_uid:t,class_name:"MouseEvent",type:"BUTTON_UP",x:n.offsetX,y:n.offsetY,modifiers:e._getModifiers(n),button:{button:a[n.button],count:1}};this.sendJsonData(r)}),!1),this.videoElt.addEventListener("touchend",(e=>{e.preventDefault();var n=e.target.getBoundingClientRect(),r={window_uid:t,class_name:"MouseEvent",type:"BUTTON_UP",x:Math.round(e.targetTouches[0].pageX-n.left),y:Math.round(e.targetTouches[0].pageY-n.top),modifiers:0,button:{button:a[e.button],count:1}};this.sendJsonData(r)}),!1),this.videoElt.addEventListener("mousemove",(n=>{n.preventDefault();var r={window_uid:t,class_name:"MouseEvent",type:0===n.buttons?"MOVE":"DRAG",x:n.offsetX,y:n.offsetY,modifiers:e._getModifiers(n),move:{buttons:n.buttons}};this.sendJsonData(r)}),!1),this.videoElt.addEventListener("touchmove",(e=>{e.preventDefault();var n=e.target.getBoundingClientRect(),r={window_uid:t,class_name:"MouseEvent",type:"DRAG",x:Math.round(e.targetTouches[0].pageX-n.left),y:Math.round(e.targetTouches[0].pageY-n.top),modifiers:0,move:{buttons:1}};this.sendJsonData(r)}),!1),this.videoElt.addEventListener("mouseleave",(n=>{var r={window_uid:t,class_name:"MouseEvent",type:"BUTTON_UP",x:n.offsetX,y:n.offsetY,modifiers:e._getModifiers(n),button:{button:a[n.button],count:1}};this.sendJsonData(r)}),!1),this.videoElt.addEventListener("wheel",(n=>{n.preventDefault();var r=n.wheelDeltaY?n.wheelDeltaY===-3*n.deltaY:0===n.deltaMode,i=n.deltaX,o=n.deltaY;i=0===i?i:-i/Math.abs(i)*1,o=0===o?o:-o/Math.abs(o)*1;var a={window_uid:t,class_name:"MouseEvent",type:"WHEEL",x:n.offsetX,y:n.offsetY,modifiers:e._getModifiers(n),wheel:{dx:i,dy:o,isTrackpad:r?1:0}};this.sendJsonData(a)}),{passive:!1})}},e.prototype.disconnect=function(){if(this.videoElt&&(this.videoElt.src=""),this.pc){e.remoteCall(this.srvurl+"/api/hangup?peerid="+this.pc.peerid,{},this.commsFetch).then(this._handleHttpErrors).catch((e=>this.onError("hangup "+e)));try{this.pc.close()}catch(e){console.warn("Failure close peer connection:"+e)}this.pc=null,this.dataChannel=null}},e.prototype.onReceiveGetIceServers=function(t,n,r,i,o){this.iceServers=t,this.pcConfig=t||{iceServers:[]};try{this.createPeerConnection();var a=this.srvurl+"/api/call?peerid="+this.pc.peerid+"&url="+encodeURIComponent(n);r&&(a+="&audiourl="+encodeURIComponent(r)),i&&(a+="&options="+encodeURIComponent(i)),o&&this.pc.addStream(o),this.earlyCandidates.length=0;var s=this;this.pc.createOffer(this.mediaConstraints).then((function(t){console.log("Create offer:"+JSON.stringify(t)),s.pc.setLocalDescription(t,(function(){e.remoteCall(a,{method:"POST",body:JSON.stringify(t)},s.commsFetch).then(s._handleHttpErrors).then((e=>e.json())).catch((e=>s.onError("call "+e))).then((e=>s.onReceiveCall.call(s,e))).catch((e=>s.onError("call "+e)))}),(function(e){console.warn("setLocalDescription error:"+JSON.stringify(e))}))}),(function(e){alert("Create offer error:"+JSON.stringify(e))}))}catch(e){this.disconnect(),alert("connect error: "+e)}},e.prototype.getIceCandidate=function(){e.remoteCall(this.srvurl+"/api/getIceCandidate?peerid="+this.pc.peerid,{},this.commsFetch).then(this._handleHttpErrors).then((e=>e.json())).then((e=>this.onReceiveCandidate.call(this,e))).catch((e=>bind.onError("getIceCandidate "+e)))},e.prototype.createPeerConnection=function(){console.log("createPeerConnection  config: "+JSON.stringify(this.pcConfig)+" option:"+JSON.stringify(this.pcOptions)),this.pc=new RTCPeerConnection(this.pcConfig,this.pcOptions);var e=this.pc;e.peerid=Math.random();var t=this;e.onicecandidate=function(e){t.onIceCandidate.call(t,e)},e.onaddstream=function(e){t.onAddStream.call(t,e)},e.oniceconnectionstatechange=function(n){console.log("oniceconnectionstatechange  state: "+e.iceConnectionState),t.videoElt&&("connected"===e.iceConnectionState?t.videoElt.style.opacity="1.0":"disconnected"===e.iceConnectionState?t.videoElt.style.opacity="0.25":"failed"===e.iceConnectionState||"closed"===e.iceConnectionState?t.videoElt.style.opacity="0.5":"new"===e.iceConnectionState&&t.getIceCandidate.call(t))},e.ondatachannel=function(e){console.log("remote datachannel created:"+JSON.stringify(e)),e.channel.onopen=function(){console.log("remote datachannel open"),t.videoElt.dispatchEvent(new CustomEvent("RemoteDataChannelOpen",{detail:e}))},e.channel.onmessage=function(e){console.log("remote datachannel recv:"+JSON.stringify(e.data))}},e.onicegatheringstatechange=function(){"complete"===e.iceGatheringState&&e.getReceivers().forEach((e=>{e.track&&"video"===e.track.kind&&void 0!==e.getParameters&&console.log("codecs:"+JSON.stringify(e.getParameters().codecs))}))};try{this.dataChannel=e.createDataChannel("ClientDataChannel");var n=this.dataChannel;n.onopen=function(){console.log("local datachannel open"),t.videoElt.dispatchEvent(new CustomEvent("LocalDataChannelOpen",{detail:{channel:n}}))},n.onmessage=function(e){console.log("local datachannel recv:"+JSON.stringify(e.data))},n.onclose=function(e){console.log("dataChannel.onclose triggered"),t.onClose()}}catch(e){console.warn("Cannot create datachannel error: "+e)}return console.log("Created RTCPeerConnection with config: "+JSON.stringify(this.pcConfig)+"option:"+JSON.stringify(this.pcOptions)),e},e.prototype.onIceCandidate=function(e){e.candidate&&e.candidate.candidate?this.pc.currentRemoteDescription?this.addIceCandidate(this.pc.peerid,e.candidate):this.earlyCandidates.push(e.candidate):console.log("End of candidates.")},e.prototype.addIceCandidate=function(t,n){e.remoteCall(this.srvurl+"/api/addIceCandidate?peerid="+t,{method:"POST",body:JSON.stringify(n)},this.commsFetch).then(this._handleHttpErrors).then((e=>e.json())).then((e=>{console.log("addIceCandidate ok:"+e)})).catch((e=>this.onError("addIceCandidate "+e)))},e.prototype.onAddStream=function(e){console.log("Remote track added:"+JSON.stringify(e)),this.videoElt.srcObject=e.stream;var t=this.videoElt.play();if(void 0!==t){var n=this;t.catch((function(e){console.warn("error:"+e),n.videoElt.setAttribute("controls",!0)}))}},e.prototype.onReceiveCall=function(e){var t=this;console.log("offer: "+JSON.stringify(e));var n=new RTCSessionDescription(e);this.pc.setRemoteDescription(n,(function(){for(console.log("setRemoteDescription ok");t.earlyCandidates.length;){var e=t.earlyCandidates.shift();t.addIceCandidate.call(t,t.pc.peerid,e)}t.getIceCandidate.call(t)}),(function(e){console.warn("setRemoteDescription error:"+JSON.stringify(e))}))},e.prototype.onReceiveCandidate=function(e){if(console.log("candidate: "+JSON.stringify(e)),e){for(var t=0;t<e.length;t++){var n=new RTCIceCandidate(e[t]);console.log("Adding ICE candidate :"+JSON.stringify(n)),this.pc.addIceCandidate(n,(function(){console.log("addIceCandidate OK")}),(function(e){console.warn("addIceCandidate error:"+JSON.stringify(e))}))}this.pc.addIceCandidate()}},e.prototype.onError=function(e){console.warn("onError:"+e)},e}();void 0!==e.exports?e.exports=t:window.WebRtcStreamer=t},543:function(e,t,n){var r;e=n.nmd(e),function(){var i,o="Expected a function",a="__lodash_hash_undefined__",s="__lodash_placeholder__",c=32,u=128,f=1/0,l=9007199254740991,d=NaN,p=4294967295,h=[["ary",u],["bind",1],["bindKey",2],["curry",8],["curryRight",16],["flip",512],["partial",c],["partialRight",64],["rearg",256]],v="[object Arguments]",m="[object Array]",g="[object Boolean]",y="[object Date]",_="[object Error]",C="[object Function]",b="[object GeneratorFunction]",w="[object Map]",S="[object Number]",T="[object Object]",R="[object Promise]",E="[object RegExp]",P="[object Set]",k="[object String]",x="[object Symbol]",O="[object WeakMap]",I="[object ArrayBuffer]",j="[object DataView]",D="[object Float32Array]",M="[object Float64Array]",L="[object Int8Array]",A="[object Int16Array]",N="[object Int32Array]",U="[object Uint8Array]",z="[object Uint8ClampedArray]",G="[object Uint16Array]",B="[object Uint32Array]",F=/\b__p \+= '';/g,W=/\b(__p \+=) '' \+/g,J=/(__e\(.*?\)|\b__t\)) \+\n'';/g,V=/&(?:amp|lt|gt|quot|#39);/g,$=/[&<>"']/g,H=RegExp(V.source),q=RegExp($.source),K=/<%-([\s\S]+?)%>/g,Y=/<%([\s\S]+?)%>/g,X=/<%=([\s\S]+?)%>/g,Z=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,Q=/^\w*$/,ee=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,te=/[\\^$.*+?()[\]{}|]/g,ne=RegExp(te.source),re=/^\s+/,ie=/\s/,oe=/\{(?:\n\/\* \[wrapped with .+\] \*\/)?\n?/,ae=/\{\n\/\* \[wrapped with (.+)\] \*/,se=/,? & /,ce=/[^\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\x7f]+/g,ue=/[()=,{}\[\]\/\s]/,fe=/\\(\\)?/g,le=/\$\{([^\\}]*(?:\\.[^\\}]*)*)\}/g,de=/\w*$/,pe=/^[-+]0x[0-9a-f]+$/i,he=/^0b[01]+$/i,ve=/^\[object .+?Constructor\]$/,me=/^0o[0-7]+$/i,ge=/^(?:0|[1-9]\d*)$/,ye=/[\xc0-\xd6\xd8-\xf6\xf8-\xff\u0100-\u017f]/g,_e=/($^)/,Ce=/['\n\r\u2028\u2029\\]/g,be="\\ud800-\\udfff",we="\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff",Se="\\u2700-\\u27bf",Te="a-z\\xdf-\\xf6\\xf8-\\xff",Re="A-Z\\xc0-\\xd6\\xd8-\\xde",Ee="\\ufe0e\\ufe0f",Pe="\\xac\\xb1\\xd7\\xf7\\x00-\\x2f\\x3a-\\x40\\x5b-\\x60\\x7b-\\xbf\\u2000-\\u206f \\t\\x0b\\f\\xa0\\ufeff\\n\\r\\u2028\\u2029\\u1680\\u180e\\u2000\\u2001\\u2002\\u2003\\u2004\\u2005\\u2006\\u2007\\u2008\\u2009\\u200a\\u202f\\u205f\\u3000",ke="["+be+"]",xe="["+Pe+"]",Oe="["+we+"]",Ie="\\d+",je="["+Se+"]",De="["+Te+"]",Me="[^"+be+Pe+Ie+Se+Te+Re+"]",Le="\\ud83c[\\udffb-\\udfff]",Ae="[^"+be+"]",Ne="(?:\\ud83c[\\udde6-\\uddff]){2}",Ue="[\\ud800-\\udbff][\\udc00-\\udfff]",ze="["+Re+"]",Ge="\\u200d",Be="(?:"+De+"|"+Me+")",Fe="(?:"+ze+"|"+Me+")",We="(?:['’](?:d|ll|m|re|s|t|ve))?",Je="(?:['’](?:D|LL|M|RE|S|T|VE))?",Ve="(?:"+Oe+"|"+Le+")?",$e="["+Ee+"]?",He=$e+Ve+"(?:"+Ge+"(?:"+[Ae,Ne,Ue].join("|")+")"+$e+Ve+")*",qe="(?:"+[je,Ne,Ue].join("|")+")"+He,Ke="(?:"+[Ae+Oe+"?",Oe,Ne,Ue,ke].join("|")+")",Ye=RegExp("['’]","g"),Xe=RegExp(Oe,"g"),Ze=RegExp(Le+"(?="+Le+")|"+Ke+He,"g"),Qe=RegExp([ze+"?"+De+"+"+We+"(?="+[xe,ze,"$"].join("|")+")",Fe+"+"+Je+"(?="+[xe,ze+Be,"$"].join("|")+")",ze+"?"+Be+"+"+We,ze+"+"+Je,"\\d*(?:1ST|2ND|3RD|(?![123])\\dTH)(?=\\b|[a-z_])","\\d*(?:1st|2nd|3rd|(?![123])\\dth)(?=\\b|[A-Z_])",Ie,qe].join("|"),"g"),et=RegExp("["+Ge+be+we+Ee+"]"),tt=/[a-z][A-Z]|[A-Z]{2}[a-z]|[0-9][a-zA-Z]|[a-zA-Z][0-9]|[^a-zA-Z0-9 ]/,nt=["Array","Buffer","DataView","Date","Error","Float32Array","Float64Array","Function","Int8Array","Int16Array","Int32Array","Map","Math","Object","Promise","RegExp","Set","String","Symbol","TypeError","Uint8Array","Uint8ClampedArray","Uint16Array","Uint32Array","WeakMap","_","clearTimeout","isFinite","parseInt","setTimeout"],rt=-1,it={};it[D]=it[M]=it[L]=it[A]=it[N]=it[U]=it[z]=it[G]=it[B]=!0,it[v]=it[m]=it[I]=it[g]=it[j]=it[y]=it[_]=it[C]=it[w]=it[S]=it[T]=it[E]=it[P]=it[k]=it[O]=!1;var ot={};ot[v]=ot[m]=ot[I]=ot[j]=ot[g]=ot[y]=ot[D]=ot[M]=ot[L]=ot[A]=ot[N]=ot[w]=ot[S]=ot[T]=ot[E]=ot[P]=ot[k]=ot[x]=ot[U]=ot[z]=ot[G]=ot[B]=!0,ot[_]=ot[C]=ot[O]=!1;var at={"\\":"\\","'":"'","\n":"n","\r":"r","\u2028":"u2028","\u2029":"u2029"},st=parseFloat,ct=parseInt,ut="object"==typeof n.g&&n.g&&n.g.Object===Object&&n.g,ft="object"==typeof self&&self&&self.Object===Object&&self,lt=ut||ft||Function("return this")(),dt=t&&!t.nodeType&&t,pt=dt&&e&&!e.nodeType&&e,ht=pt&&pt.exports===dt,vt=ht&&ut.process,mt=function(){try{return pt&&pt.require&&pt.require("util").types||vt&&vt.binding&&vt.binding("util")}catch(e){}}(),gt=mt&&mt.isArrayBuffer,yt=mt&&mt.isDate,_t=mt&&mt.isMap,Ct=mt&&mt.isRegExp,bt=mt&&mt.isSet,wt=mt&&mt.isTypedArray;function St(e,t,n){switch(n.length){case 0:return e.call(t);case 1:return e.call(t,n[0]);case 2:return e.call(t,n[0],n[1]);case 3:return e.call(t,n[0],n[1],n[2])}return e.apply(t,n)}function Tt(e,t,n,r){for(var i=-1,o=null==e?0:e.length;++i<o;){var a=e[i];t(r,a,n(a),e)}return r}function Rt(e,t){for(var n=-1,r=null==e?0:e.length;++n<r&&!1!==t(e[n],n,e););return e}function Et(e,t){for(var n=null==e?0:e.length;n--&&!1!==t(e[n],n,e););return e}function Pt(e,t){for(var n=-1,r=null==e?0:e.length;++n<r;)if(!t(e[n],n,e))return!1;return!0}function kt(e,t){for(var n=-1,r=null==e?0:e.length,i=0,o=[];++n<r;){var a=e[n];t(a,n,e)&&(o[i++]=a)}return o}function xt(e,t){return!(null==e||!e.length)&&zt(e,t,0)>-1}function Ot(e,t,n){for(var r=-1,i=null==e?0:e.length;++r<i;)if(n(t,e[r]))return!0;return!1}function It(e,t){for(var n=-1,r=null==e?0:e.length,i=Array(r);++n<r;)i[n]=t(e[n],n,e);return i}function jt(e,t){for(var n=-1,r=t.length,i=e.length;++n<r;)e[i+n]=t[n];return e}function Dt(e,t,n,r){var i=-1,o=null==e?0:e.length;for(r&&o&&(n=e[++i]);++i<o;)n=t(n,e[i],i,e);return n}function Mt(e,t,n,r){var i=null==e?0:e.length;for(r&&i&&(n=e[--i]);i--;)n=t(n,e[i],i,e);return n}function Lt(e,t){for(var n=-1,r=null==e?0:e.length;++n<r;)if(t(e[n],n,e))return!0;return!1}var At=Wt("length");function Nt(e,t,n){var r;return n(e,(function(e,n,i){if(t(e,n,i))return r=n,!1})),r}function Ut(e,t,n,r){for(var i=e.length,o=n+(r?1:-1);r?o--:++o<i;)if(t(e[o],o,e))return o;return-1}function zt(e,t,n){return t==t?function(e,t,n){for(var r=n-1,i=e.length;++r<i;)if(e[r]===t)return r;return-1}(e,t,n):Ut(e,Bt,n)}function Gt(e,t,n,r){for(var i=n-1,o=e.length;++i<o;)if(r(e[i],t))return i;return-1}function Bt(e){return e!=e}function Ft(e,t){var n=null==e?0:e.length;return n?$t(e,t)/n:d}function Wt(e){return function(t){return null==t?i:t[e]}}function Jt(e){return function(t){return null==e?i:e[t]}}function Vt(e,t,n,r,i){return i(e,(function(e,i,o){n=r?(r=!1,e):t(n,e,i,o)})),n}function $t(e,t){for(var n,r=-1,o=e.length;++r<o;){var a=t(e[r]);a!==i&&(n=n===i?a:n+a)}return n}function Ht(e,t){for(var n=-1,r=Array(e);++n<e;)r[n]=t(n);return r}function qt(e){return e?e.slice(0,dn(e)+1).replace(re,""):e}function Kt(e){return function(t){return e(t)}}function Yt(e,t){return It(t,(function(t){return e[t]}))}function Xt(e,t){return e.has(t)}function Zt(e,t){for(var n=-1,r=e.length;++n<r&&zt(t,e[n],0)>-1;);return n}function Qt(e,t){for(var n=e.length;n--&&zt(t,e[n],0)>-1;);return n}var en=Jt({À:"A",Á:"A",Â:"A",Ã:"A",Ä:"A",Å:"A",à:"a",á:"a",â:"a",ã:"a",ä:"a",å:"a",Ç:"C",ç:"c",Ð:"D",ð:"d",È:"E",É:"E",Ê:"E",Ë:"E",è:"e",é:"e",ê:"e",ë:"e",Ì:"I",Í:"I",Î:"I",Ï:"I",ì:"i",í:"i",î:"i",ï:"i",Ñ:"N",ñ:"n",Ò:"O",Ó:"O",Ô:"O",Õ:"O",Ö:"O",Ø:"O",ò:"o",ó:"o",ô:"o",õ:"o",ö:"o",ø:"o",Ù:"U",Ú:"U",Û:"U",Ü:"U",ù:"u",ú:"u",û:"u",ü:"u",Ý:"Y",ý:"y",ÿ:"y",Æ:"Ae",æ:"ae",Þ:"Th",þ:"th",ß:"ss",Ā:"A",Ă:"A",Ą:"A",ā:"a",ă:"a",ą:"a",Ć:"C",Ĉ:"C",Ċ:"C",Č:"C",ć:"c",ĉ:"c",ċ:"c",č:"c",Ď:"D",Đ:"D",ď:"d",đ:"d",Ē:"E",Ĕ:"E",Ė:"E",Ę:"E",Ě:"E",ē:"e",ĕ:"e",ė:"e",ę:"e",ě:"e",Ĝ:"G",Ğ:"G",Ġ:"G",Ģ:"G",ĝ:"g",ğ:"g",ġ:"g",ģ:"g",Ĥ:"H",Ħ:"H",ĥ:"h",ħ:"h",Ĩ:"I",Ī:"I",Ĭ:"I",Į:"I",İ:"I",ĩ:"i",ī:"i",ĭ:"i",į:"i",ı:"i",Ĵ:"J",ĵ:"j",Ķ:"K",ķ:"k",ĸ:"k",Ĺ:"L",Ļ:"L",Ľ:"L",Ŀ:"L",Ł:"L",ĺ:"l",ļ:"l",ľ:"l",ŀ:"l",ł:"l",Ń:"N",Ņ:"N",Ň:"N",Ŋ:"N",ń:"n",ņ:"n",ň:"n",ŋ:"n",Ō:"O",Ŏ:"O",Ő:"O",ō:"o",ŏ:"o",ő:"o",Ŕ:"R",Ŗ:"R",Ř:"R",ŕ:"r",ŗ:"r",ř:"r",Ś:"S",Ŝ:"S",Ş:"S",Š:"S",ś:"s",ŝ:"s",ş:"s",š:"s",Ţ:"T",Ť:"T",Ŧ:"T",ţ:"t",ť:"t",ŧ:"t",Ũ:"U",Ū:"U",Ŭ:"U",Ů:"U",Ű:"U",Ų:"U",ũ:"u",ū:"u",ŭ:"u",ů:"u",ű:"u",ų:"u",Ŵ:"W",ŵ:"w",Ŷ:"Y",ŷ:"y",Ÿ:"Y",Ź:"Z",Ż:"Z",Ž:"Z",ź:"z",ż:"z",ž:"z",Ĳ:"IJ",ĳ:"ij",Œ:"Oe",œ:"oe",ŉ:"'n",ſ:"s"}),tn=Jt({"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"});function nn(e){return"\\"+at[e]}function rn(e){return et.test(e)}function on(e){var t=-1,n=Array(e.size);return e.forEach((function(e,r){n[++t]=[r,e]})),n}function an(e,t){return function(n){return e(t(n))}}function sn(e,t){for(var n=-1,r=e.length,i=0,o=[];++n<r;){var a=e[n];a!==t&&a!==s||(e[n]=s,o[i++]=n)}return o}function cn(e){var t=-1,n=Array(e.size);return e.forEach((function(e){n[++t]=e})),n}function un(e){var t=-1,n=Array(e.size);return e.forEach((function(e){n[++t]=[e,e]})),n}function fn(e){return rn(e)?function(e){for(var t=Ze.lastIndex=0;Ze.test(e);)++t;return t}(e):At(e)}function ln(e){return rn(e)?function(e){return e.match(Ze)||[]}(e):function(e){return e.split("")}(e)}function dn(e){for(var t=e.length;t--&&ie.test(e.charAt(t)););return t}var pn=Jt({"&amp;":"&","&lt;":"<","&gt;":">","&quot;":'"',"&#39;":"'"}),hn=function e(t){var n,r=(t=null==t?lt:hn.defaults(lt.Object(),t,hn.pick(lt,nt))).Array,ie=t.Date,be=t.Error,we=t.Function,Se=t.Math,Te=t.Object,Re=t.RegExp,Ee=t.String,Pe=t.TypeError,ke=r.prototype,xe=we.prototype,Oe=Te.prototype,Ie=t["__core-js_shared__"],je=xe.toString,De=Oe.hasOwnProperty,Me=0,Le=(n=/[^.]+$/.exec(Ie&&Ie.keys&&Ie.keys.IE_PROTO||""))?"Symbol(src)_1."+n:"",Ae=Oe.toString,Ne=je.call(Te),Ue=lt._,ze=Re("^"+je.call(De).replace(te,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),Ge=ht?t.Buffer:i,Be=t.Symbol,Fe=t.Uint8Array,We=Ge?Ge.allocUnsafe:i,Je=an(Te.getPrototypeOf,Te),Ve=Te.create,$e=Oe.propertyIsEnumerable,He=ke.splice,qe=Be?Be.isConcatSpreadable:i,Ke=Be?Be.iterator:i,Ze=Be?Be.toStringTag:i,et=function(){try{var e=co(Te,"defineProperty");return e({},"",{}),e}catch(e){}}(),at=t.clearTimeout!==lt.clearTimeout&&t.clearTimeout,ut=ie&&ie.now!==lt.Date.now&&ie.now,ft=t.setTimeout!==lt.setTimeout&&t.setTimeout,dt=Se.ceil,pt=Se.floor,vt=Te.getOwnPropertySymbols,mt=Ge?Ge.isBuffer:i,At=t.isFinite,Jt=ke.join,vn=an(Te.keys,Te),mn=Se.max,gn=Se.min,yn=ie.now,_n=t.parseInt,Cn=Se.random,bn=ke.reverse,wn=co(t,"DataView"),Sn=co(t,"Map"),Tn=co(t,"Promise"),Rn=co(t,"Set"),En=co(t,"WeakMap"),Pn=co(Te,"create"),kn=En&&new En,xn={},On=No(wn),In=No(Sn),jn=No(Tn),Dn=No(Rn),Mn=No(En),Ln=Be?Be.prototype:i,An=Ln?Ln.valueOf:i,Nn=Ln?Ln.toString:i;function Un(e){if(es(e)&&!Wa(e)&&!(e instanceof Fn)){if(e instanceof Bn)return e;if(De.call(e,"__wrapped__"))return Uo(e)}return new Bn(e)}var zn=function(){function e(){}return function(t){if(!Qa(t))return{};if(Ve)return Ve(t);e.prototype=t;var n=new e;return e.prototype=i,n}}();function Gn(){}function Bn(e,t){this.__wrapped__=e,this.__actions__=[],this.__chain__=!!t,this.__index__=0,this.__values__=i}function Fn(e){this.__wrapped__=e,this.__actions__=[],this.__dir__=1,this.__filtered__=!1,this.__iteratees__=[],this.__takeCount__=p,this.__views__=[]}function Wn(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}function Jn(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}function Vn(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}function $n(e){var t=-1,n=null==e?0:e.length;for(this.__data__=new Vn;++t<n;)this.add(e[t])}function Hn(e){var t=this.__data__=new Jn(e);this.size=t.size}function qn(e,t){var n=Wa(e),r=!n&&Fa(e),i=!n&&!r&&Ha(e),o=!n&&!r&&!i&&cs(e),a=n||r||i||o,s=a?Ht(e.length,Ee):[],c=s.length;for(var u in e)!t&&!De.call(e,u)||a&&("length"==u||i&&("offset"==u||"parent"==u)||o&&("buffer"==u||"byteLength"==u||"byteOffset"==u)||mo(u,c))||s.push(u);return s}function Kn(e){var t=e.length;return t?e[Vr(0,t-1)]:i}function Yn(e,t){return jo(Ei(e),or(t,0,e.length))}function Xn(e){return jo(Ei(e))}function Zn(e,t,n){(n!==i&&!za(e[t],n)||n===i&&!(t in e))&&rr(e,t,n)}function Qn(e,t,n){var r=e[t];De.call(e,t)&&za(r,n)&&(n!==i||t in e)||rr(e,t,n)}function er(e,t){for(var n=e.length;n--;)if(za(e[n][0],t))return n;return-1}function tr(e,t,n,r){return fr(e,(function(e,i,o){t(r,e,n(e),o)})),r}function nr(e,t){return e&&Pi(t,Os(t),e)}function rr(e,t,n){"__proto__"==t&&et?et(e,t,{configurable:!0,enumerable:!0,value:n,writable:!0}):e[t]=n}function ir(e,t){for(var n=-1,o=t.length,a=r(o),s=null==e;++n<o;)a[n]=s?i:Rs(e,t[n]);return a}function or(e,t,n){return e==e&&(n!==i&&(e=e<=n?e:n),t!==i&&(e=e>=t?e:t)),e}function ar(e,t,n,r,o,a){var s,c=1&t,u=2&t,f=4&t;if(n&&(s=o?n(e,r,o,a):n(e)),s!==i)return s;if(!Qa(e))return e;var l=Wa(e);if(l){if(s=function(e){var t=e.length,n=new e.constructor(t);return t&&"string"==typeof e[0]&&De.call(e,"index")&&(n.index=e.index,n.input=e.input),n}(e),!c)return Ei(e,s)}else{var d=lo(e),p=d==C||d==b;if(Ha(e))return Ci(e,c);if(d==T||d==v||p&&!o){if(s=u||p?{}:ho(e),!c)return u?function(e,t){return Pi(e,fo(e),t)}(e,function(e,t){return e&&Pi(t,Is(t),e)}(s,e)):function(e,t){return Pi(e,uo(e),t)}(e,nr(s,e))}else{if(!ot[d])return o?e:{};s=function(e,t,n){var r,i=e.constructor;switch(t){case I:return bi(e);case g:case y:return new i(+e);case j:return function(e,t){var n=t?bi(e.buffer):e.buffer;return new e.constructor(n,e.byteOffset,e.byteLength)}(e,n);case D:case M:case L:case A:case N:case U:case z:case G:case B:return wi(e,n);case w:return new i;case S:case k:return new i(e);case E:return function(e){var t=new e.constructor(e.source,de.exec(e));return t.lastIndex=e.lastIndex,t}(e);case P:return new i;case x:return r=e,An?Te(An.call(r)):{}}}(e,d,c)}}a||(a=new Hn);var h=a.get(e);if(h)return h;a.set(e,s),os(e)?e.forEach((function(r){s.add(ar(r,t,n,r,e,a))})):ts(e)&&e.forEach((function(r,i){s.set(i,ar(r,t,n,i,e,a))}));var m=l?i:(f?u?to:eo:u?Is:Os)(e);return Rt(m||e,(function(r,i){m&&(r=e[i=r]),Qn(s,i,ar(r,t,n,i,e,a))})),s}function sr(e,t,n){var r=n.length;if(null==e)return!r;for(e=Te(e);r--;){var o=n[r],a=t[o],s=e[o];if(s===i&&!(o in e)||!a(s))return!1}return!0}function cr(e,t,n){if("function"!=typeof e)throw new Pe(o);return ko((function(){e.apply(i,n)}),t)}function ur(e,t,n,r){var i=-1,o=xt,a=!0,s=e.length,c=[],u=t.length;if(!s)return c;n&&(t=It(t,Kt(n))),r?(o=Ot,a=!1):t.length>=200&&(o=Xt,a=!1,t=new $n(t));e:for(;++i<s;){var f=e[i],l=null==n?f:n(f);if(f=r||0!==f?f:0,a&&l==l){for(var d=u;d--;)if(t[d]===l)continue e;c.push(f)}else o(t,l,r)||c.push(f)}return c}Un.templateSettings={escape:K,evaluate:Y,interpolate:X,variable:"",imports:{_:Un}},Un.prototype=Gn.prototype,Un.prototype.constructor=Un,Bn.prototype=zn(Gn.prototype),Bn.prototype.constructor=Bn,Fn.prototype=zn(Gn.prototype),Fn.prototype.constructor=Fn,Wn.prototype.clear=function(){this.__data__=Pn?Pn(null):{},this.size=0},Wn.prototype.delete=function(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t},Wn.prototype.get=function(e){var t=this.__data__;if(Pn){var n=t[e];return n===a?i:n}return De.call(t,e)?t[e]:i},Wn.prototype.has=function(e){var t=this.__data__;return Pn?t[e]!==i:De.call(t,e)},Wn.prototype.set=function(e,t){var n=this.__data__;return this.size+=this.has(e)?0:1,n[e]=Pn&&t===i?a:t,this},Jn.prototype.clear=function(){this.__data__=[],this.size=0},Jn.prototype.delete=function(e){var t=this.__data__,n=er(t,e);return!(n<0||(n==t.length-1?t.pop():He.call(t,n,1),--this.size,0))},Jn.prototype.get=function(e){var t=this.__data__,n=er(t,e);return n<0?i:t[n][1]},Jn.prototype.has=function(e){return er(this.__data__,e)>-1},Jn.prototype.set=function(e,t){var n=this.__data__,r=er(n,e);return r<0?(++this.size,n.push([e,t])):n[r][1]=t,this},Vn.prototype.clear=function(){this.size=0,this.__data__={hash:new Wn,map:new(Sn||Jn),string:new Wn}},Vn.prototype.delete=function(e){var t=ao(this,e).delete(e);return this.size-=t?1:0,t},Vn.prototype.get=function(e){return ao(this,e).get(e)},Vn.prototype.has=function(e){return ao(this,e).has(e)},Vn.prototype.set=function(e,t){var n=ao(this,e),r=n.size;return n.set(e,t),this.size+=n.size==r?0:1,this},$n.prototype.add=$n.prototype.push=function(e){return this.__data__.set(e,a),this},$n.prototype.has=function(e){return this.__data__.has(e)},Hn.prototype.clear=function(){this.__data__=new Jn,this.size=0},Hn.prototype.delete=function(e){var t=this.__data__,n=t.delete(e);return this.size=t.size,n},Hn.prototype.get=function(e){return this.__data__.get(e)},Hn.prototype.has=function(e){return this.__data__.has(e)},Hn.prototype.set=function(e,t){var n=this.__data__;if(n instanceof Jn){var r=n.__data__;if(!Sn||r.length<199)return r.push([e,t]),this.size=++n.size,this;n=this.__data__=new Vn(r)}return n.set(e,t),this.size=n.size,this};var fr=Oi(yr),lr=Oi(_r,!0);function dr(e,t){var n=!0;return fr(e,(function(e,r,i){return n=!!t(e,r,i)})),n}function pr(e,t,n){for(var r=-1,o=e.length;++r<o;){var a=e[r],s=t(a);if(null!=s&&(c===i?s==s&&!ss(s):n(s,c)))var c=s,u=a}return u}function hr(e,t){var n=[];return fr(e,(function(e,r,i){t(e,r,i)&&n.push(e)})),n}function vr(e,t,n,r,i){var o=-1,a=e.length;for(n||(n=vo),i||(i=[]);++o<a;){var s=e[o];t>0&&n(s)?t>1?vr(s,t-1,n,r,i):jt(i,s):r||(i[i.length]=s)}return i}var mr=Ii(),gr=Ii(!0);function yr(e,t){return e&&mr(e,t,Os)}function _r(e,t){return e&&gr(e,t,Os)}function Cr(e,t){return kt(t,(function(t){return Ya(e[t])}))}function br(e,t){for(var n=0,r=(t=mi(t,e)).length;null!=e&&n<r;)e=e[Ao(t[n++])];return n&&n==r?e:i}function wr(e,t,n){var r=t(e);return Wa(e)?r:jt(r,n(e))}function Sr(e){return null==e?e===i?"[object Undefined]":"[object Null]":Ze&&Ze in Te(e)?function(e){var t=De.call(e,Ze),n=e[Ze];try{e[Ze]=i;var r=!0}catch(e){}var o=Ae.call(e);return r&&(t?e[Ze]=n:delete e[Ze]),o}(e):function(e){return Ae.call(e)}(e)}function Tr(e,t){return e>t}function Rr(e,t){return null!=e&&De.call(e,t)}function Er(e,t){return null!=e&&t in Te(e)}function Pr(e,t,n){for(var o=n?Ot:xt,a=e[0].length,s=e.length,c=s,u=r(s),f=1/0,l=[];c--;){var d=e[c];c&&t&&(d=It(d,Kt(t))),f=gn(d.length,f),u[c]=!n&&(t||a>=120&&d.length>=120)?new $n(c&&d):i}d=e[0];var p=-1,h=u[0];e:for(;++p<a&&l.length<f;){var v=d[p],m=t?t(v):v;if(v=n||0!==v?v:0,!(h?Xt(h,m):o(l,m,n))){for(c=s;--c;){var g=u[c];if(!(g?Xt(g,m):o(e[c],m,n)))continue e}h&&h.push(m),l.push(v)}}return l}function kr(e,t,n){var r=null==(e=Ro(e,t=mi(t,e)))?e:e[Ao(Ko(t))];return null==r?i:St(r,e,n)}function xr(e){return es(e)&&Sr(e)==v}function Or(e,t,n,r,o){return e===t||(null==e||null==t||!es(e)&&!es(t)?e!=e&&t!=t:function(e,t,n,r,o,a){var s=Wa(e),c=Wa(t),u=s?m:lo(e),f=c?m:lo(t),l=(u=u==v?T:u)==T,d=(f=f==v?T:f)==T,p=u==f;if(p&&Ha(e)){if(!Ha(t))return!1;s=!0,l=!1}if(p&&!l)return a||(a=new Hn),s||cs(e)?Zi(e,t,n,r,o,a):function(e,t,n,r,i,o,a){switch(n){case j:if(e.byteLength!=t.byteLength||e.byteOffset!=t.byteOffset)return!1;e=e.buffer,t=t.buffer;case I:return!(e.byteLength!=t.byteLength||!o(new Fe(e),new Fe(t)));case g:case y:case S:return za(+e,+t);case _:return e.name==t.name&&e.message==t.message;case E:case k:return e==t+"";case w:var s=on;case P:var c=1&r;if(s||(s=cn),e.size!=t.size&&!c)return!1;var u=a.get(e);if(u)return u==t;r|=2,a.set(e,t);var f=Zi(s(e),s(t),r,i,o,a);return a.delete(e),f;case x:if(An)return An.call(e)==An.call(t)}return!1}(e,t,u,n,r,o,a);if(!(1&n)){var h=l&&De.call(e,"__wrapped__"),C=d&&De.call(t,"__wrapped__");if(h||C){var b=h?e.value():e,R=C?t.value():t;return a||(a=new Hn),o(b,R,n,r,a)}}return!!p&&(a||(a=new Hn),function(e,t,n,r,o,a){var s=1&n,c=eo(e),u=c.length;if(u!=eo(t).length&&!s)return!1;for(var f=u;f--;){var l=c[f];if(!(s?l in t:De.call(t,l)))return!1}var d=a.get(e),p=a.get(t);if(d&&p)return d==t&&p==e;var h=!0;a.set(e,t),a.set(t,e);for(var v=s;++f<u;){var m=e[l=c[f]],g=t[l];if(r)var y=s?r(g,m,l,t,e,a):r(m,g,l,e,t,a);if(!(y===i?m===g||o(m,g,n,r,a):y)){h=!1;break}v||(v="constructor"==l)}if(h&&!v){var _=e.constructor,C=t.constructor;_==C||!("constructor"in e)||!("constructor"in t)||"function"==typeof _&&_ instanceof _&&"function"==typeof C&&C instanceof C||(h=!1)}return a.delete(e),a.delete(t),h}(e,t,n,r,o,a))}(e,t,n,r,Or,o))}function Ir(e,t,n,r){var o=n.length,a=o,s=!r;if(null==e)return!a;for(e=Te(e);o--;){var c=n[o];if(s&&c[2]?c[1]!==e[c[0]]:!(c[0]in e))return!1}for(;++o<a;){var u=(c=n[o])[0],f=e[u],l=c[1];if(s&&c[2]){if(f===i&&!(u in e))return!1}else{var d=new Hn;if(r)var p=r(f,l,u,e,t,d);if(!(p===i?Or(l,f,3,r,d):p))return!1}}return!0}function jr(e){return!(!Qa(e)||(t=e,Le&&Le in t))&&(Ya(e)?ze:ve).test(No(e));var t}function Dr(e){return"function"==typeof e?e:null==e?nc:"object"==typeof e?Wa(e)?Ur(e[0],e[1]):Nr(e):lc(e)}function Mr(e){if(!bo(e))return vn(e);var t=[];for(var n in Te(e))De.call(e,n)&&"constructor"!=n&&t.push(n);return t}function Lr(e,t){return e<t}function Ar(e,t){var n=-1,i=Va(e)?r(e.length):[];return fr(e,(function(e,r,o){i[++n]=t(e,r,o)})),i}function Nr(e){var t=so(e);return 1==t.length&&t[0][2]?So(t[0][0],t[0][1]):function(n){return n===e||Ir(n,e,t)}}function Ur(e,t){return yo(e)&&wo(t)?So(Ao(e),t):function(n){var r=Rs(n,e);return r===i&&r===t?Es(n,e):Or(t,r,3)}}function zr(e,t,n,r,o){e!==t&&mr(t,(function(a,s){if(o||(o=new Hn),Qa(a))!function(e,t,n,r,o,a,s){var c=Eo(e,n),u=Eo(t,n),f=s.get(u);if(f)Zn(e,n,f);else{var l=a?a(c,u,n+"",e,t,s):i,d=l===i;if(d){var p=Wa(u),h=!p&&Ha(u),v=!p&&!h&&cs(u);l=u,p||h||v?Wa(c)?l=c:$a(c)?l=Ei(c):h?(d=!1,l=Ci(u,!0)):v?(d=!1,l=wi(u,!0)):l=[]:rs(u)||Fa(u)?(l=c,Fa(c)?l=ms(c):Qa(c)&&!Ya(c)||(l=ho(u))):d=!1}d&&(s.set(u,l),o(l,u,r,a,s),s.delete(u)),Zn(e,n,l)}}(e,t,s,n,zr,r,o);else{var c=r?r(Eo(e,s),a,s+"",e,t,o):i;c===i&&(c=a),Zn(e,s,c)}}),Is)}function Gr(e,t){var n=e.length;if(n)return mo(t+=t<0?n:0,n)?e[t]:i}function Br(e,t,n){t=t.length?It(t,(function(e){return Wa(e)?function(t){return br(t,1===e.length?e[0]:e)}:e})):[nc];var r=-1;t=It(t,Kt(oo()));var i=Ar(e,(function(e,n,i){var o=It(t,(function(t){return t(e)}));return{criteria:o,index:++r,value:e}}));return function(e){var t=e.length;for(e.sort((function(e,t){return function(e,t,n){for(var r=-1,i=e.criteria,o=t.criteria,a=i.length,s=n.length;++r<a;){var c=Si(i[r],o[r]);if(c)return r>=s?c:c*("desc"==n[r]?-1:1)}return e.index-t.index}(e,t,n)}));t--;)e[t]=e[t].value;return e}(i)}function Fr(e,t,n){for(var r=-1,i=t.length,o={};++r<i;){var a=t[r],s=br(e,a);n(s,a)&&Yr(o,mi(a,e),s)}return o}function Wr(e,t,n,r){var i=r?Gt:zt,o=-1,a=t.length,s=e;for(e===t&&(t=Ei(t)),n&&(s=It(e,Kt(n)));++o<a;)for(var c=0,u=t[o],f=n?n(u):u;(c=i(s,f,c,r))>-1;)s!==e&&He.call(s,c,1),He.call(e,c,1);return e}function Jr(e,t){for(var n=e?t.length:0,r=n-1;n--;){var i=t[n];if(n==r||i!==o){var o=i;mo(i)?He.call(e,i,1):ci(e,i)}}return e}function Vr(e,t){return e+pt(Cn()*(t-e+1))}function $r(e,t){var n="";if(!e||t<1||t>l)return n;do{t%2&&(n+=e),(t=pt(t/2))&&(e+=e)}while(t);return n}function Hr(e,t){return xo(To(e,t,nc),e+"")}function qr(e){return Kn(zs(e))}function Kr(e,t){var n=zs(e);return jo(n,or(t,0,n.length))}function Yr(e,t,n,r){if(!Qa(e))return e;for(var o=-1,a=(t=mi(t,e)).length,s=a-1,c=e;null!=c&&++o<a;){var u=Ao(t[o]),f=n;if("__proto__"===u||"constructor"===u||"prototype"===u)return e;if(o!=s){var l=c[u];(f=r?r(l,u,c):i)===i&&(f=Qa(l)?l:mo(t[o+1])?[]:{})}Qn(c,u,f),c=c[u]}return e}var Xr=kn?function(e,t){return kn.set(e,t),e}:nc,Zr=et?function(e,t){return et(e,"toString",{configurable:!0,enumerable:!1,value:Qs(t),writable:!0})}:nc;function Qr(e){return jo(zs(e))}function ei(e,t,n){var i=-1,o=e.length;t<0&&(t=-t>o?0:o+t),(n=n>o?o:n)<0&&(n+=o),o=t>n?0:n-t>>>0,t>>>=0;for(var a=r(o);++i<o;)a[i]=e[i+t];return a}function ti(e,t){var n;return fr(e,(function(e,r,i){return!(n=t(e,r,i))})),!!n}function ni(e,t,n){var r=0,i=null==e?r:e.length;if("number"==typeof t&&t==t&&i<=2147483647){for(;r<i;){var o=r+i>>>1,a=e[o];null!==a&&!ss(a)&&(n?a<=t:a<t)?r=o+1:i=o}return i}return ri(e,t,nc,n)}function ri(e,t,n,r){var o=0,a=null==e?0:e.length;if(0===a)return 0;for(var s=(t=n(t))!=t,c=null===t,u=ss(t),f=t===i;o<a;){var l=pt((o+a)/2),d=n(e[l]),p=d!==i,h=null===d,v=d==d,m=ss(d);if(s)var g=r||v;else g=f?v&&(r||p):c?v&&p&&(r||!h):u?v&&p&&!h&&(r||!m):!h&&!m&&(r?d<=t:d<t);g?o=l+1:a=l}return gn(a,4294967294)}function ii(e,t){for(var n=-1,r=e.length,i=0,o=[];++n<r;){var a=e[n],s=t?t(a):a;if(!n||!za(s,c)){var c=s;o[i++]=0===a?0:a}}return o}function oi(e){return"number"==typeof e?e:ss(e)?d:+e}function ai(e){if("string"==typeof e)return e;if(Wa(e))return It(e,ai)+"";if(ss(e))return Nn?Nn.call(e):"";var t=e+"";return"0"==t&&1/e==-1/0?"-0":t}function si(e,t,n){var r=-1,i=xt,o=e.length,a=!0,s=[],c=s;if(n)a=!1,i=Ot;else if(o>=200){var u=t?null:$i(e);if(u)return cn(u);a=!1,i=Xt,c=new $n}else c=t?[]:s;e:for(;++r<o;){var f=e[r],l=t?t(f):f;if(f=n||0!==f?f:0,a&&l==l){for(var d=c.length;d--;)if(c[d]===l)continue e;t&&c.push(l),s.push(f)}else i(c,l,n)||(c!==s&&c.push(l),s.push(f))}return s}function ci(e,t){return null==(e=Ro(e,t=mi(t,e)))||delete e[Ao(Ko(t))]}function ui(e,t,n,r){return Yr(e,t,n(br(e,t)),r)}function fi(e,t,n,r){for(var i=e.length,o=r?i:-1;(r?o--:++o<i)&&t(e[o],o,e););return n?ei(e,r?0:o,r?o+1:i):ei(e,r?o+1:0,r?i:o)}function li(e,t){var n=e;return n instanceof Fn&&(n=n.value()),Dt(t,(function(e,t){return t.func.apply(t.thisArg,jt([e],t.args))}),n)}function di(e,t,n){var i=e.length;if(i<2)return i?si(e[0]):[];for(var o=-1,a=r(i);++o<i;)for(var s=e[o],c=-1;++c<i;)c!=o&&(a[o]=ur(a[o]||s,e[c],t,n));return si(vr(a,1),t,n)}function pi(e,t,n){for(var r=-1,o=e.length,a=t.length,s={};++r<o;){var c=r<a?t[r]:i;n(s,e[r],c)}return s}function hi(e){return $a(e)?e:[]}function vi(e){return"function"==typeof e?e:nc}function mi(e,t){return Wa(e)?e:yo(e,t)?[e]:Lo(gs(e))}var gi=Hr;function yi(e,t,n){var r=e.length;return n=n===i?r:n,!t&&n>=r?e:ei(e,t,n)}var _i=at||function(e){return lt.clearTimeout(e)};function Ci(e,t){if(t)return e.slice();var n=e.length,r=We?We(n):new e.constructor(n);return e.copy(r),r}function bi(e){var t=new e.constructor(e.byteLength);return new Fe(t).set(new Fe(e)),t}function wi(e,t){var n=t?bi(e.buffer):e.buffer;return new e.constructor(n,e.byteOffset,e.length)}function Si(e,t){if(e!==t){var n=e!==i,r=null===e,o=e==e,a=ss(e),s=t!==i,c=null===t,u=t==t,f=ss(t);if(!c&&!f&&!a&&e>t||a&&s&&u&&!c&&!f||r&&s&&u||!n&&u||!o)return 1;if(!r&&!a&&!f&&e<t||f&&n&&o&&!r&&!a||c&&n&&o||!s&&o||!u)return-1}return 0}function Ti(e,t,n,i){for(var o=-1,a=e.length,s=n.length,c=-1,u=t.length,f=mn(a-s,0),l=r(u+f),d=!i;++c<u;)l[c]=t[c];for(;++o<s;)(d||o<a)&&(l[n[o]]=e[o]);for(;f--;)l[c++]=e[o++];return l}function Ri(e,t,n,i){for(var o=-1,a=e.length,s=-1,c=n.length,u=-1,f=t.length,l=mn(a-c,0),d=r(l+f),p=!i;++o<l;)d[o]=e[o];for(var h=o;++u<f;)d[h+u]=t[u];for(;++s<c;)(p||o<a)&&(d[h+n[s]]=e[o++]);return d}function Ei(e,t){var n=-1,i=e.length;for(t||(t=r(i));++n<i;)t[n]=e[n];return t}function Pi(e,t,n,r){var o=!n;n||(n={});for(var a=-1,s=t.length;++a<s;){var c=t[a],u=r?r(n[c],e[c],c,n,e):i;u===i&&(u=e[c]),o?rr(n,c,u):Qn(n,c,u)}return n}function ki(e,t){return function(n,r){var i=Wa(n)?Tt:tr,o=t?t():{};return i(n,e,oo(r,2),o)}}function xi(e){return Hr((function(t,n){var r=-1,o=n.length,a=o>1?n[o-1]:i,s=o>2?n[2]:i;for(a=e.length>3&&"function"==typeof a?(o--,a):i,s&&go(n[0],n[1],s)&&(a=o<3?i:a,o=1),t=Te(t);++r<o;){var c=n[r];c&&e(t,c,r,a)}return t}))}function Oi(e,t){return function(n,r){if(null==n)return n;if(!Va(n))return e(n,r);for(var i=n.length,o=t?i:-1,a=Te(n);(t?o--:++o<i)&&!1!==r(a[o],o,a););return n}}function Ii(e){return function(t,n,r){for(var i=-1,o=Te(t),a=r(t),s=a.length;s--;){var c=a[e?s:++i];if(!1===n(o[c],c,o))break}return t}}function ji(e){return function(t){var n=rn(t=gs(t))?ln(t):i,r=n?n[0]:t.charAt(0),o=n?yi(n,1).join(""):t.slice(1);return r[e]()+o}}function Di(e){return function(t){return Dt(Ys(Fs(t).replace(Ye,"")),e,"")}}function Mi(e){return function(){var t=arguments;switch(t.length){case 0:return new e;case 1:return new e(t[0]);case 2:return new e(t[0],t[1]);case 3:return new e(t[0],t[1],t[2]);case 4:return new e(t[0],t[1],t[2],t[3]);case 5:return new e(t[0],t[1],t[2],t[3],t[4]);case 6:return new e(t[0],t[1],t[2],t[3],t[4],t[5]);case 7:return new e(t[0],t[1],t[2],t[3],t[4],t[5],t[6])}var n=zn(e.prototype),r=e.apply(n,t);return Qa(r)?r:n}}function Li(e){return function(t,n,r){var o=Te(t);if(!Va(t)){var a=oo(n,3);t=Os(t),n=function(e){return a(o[e],e,o)}}var s=e(t,n,r);return s>-1?o[a?t[s]:s]:i}}function Ai(e){return Qi((function(t){var n=t.length,r=n,a=Bn.prototype.thru;for(e&&t.reverse();r--;){var s=t[r];if("function"!=typeof s)throw new Pe(o);if(a&&!c&&"wrapper"==ro(s))var c=new Bn([],!0)}for(r=c?r:n;++r<n;){var u=ro(s=t[r]),f="wrapper"==u?no(s):i;c=f&&_o(f[0])&&424==f[1]&&!f[4].length&&1==f[9]?c[ro(f[0])].apply(c,f[3]):1==s.length&&_o(s)?c[u]():c.thru(s)}return function(){var e=arguments,r=e[0];if(c&&1==e.length&&Wa(r))return c.plant(r).value();for(var i=0,o=n?t[i].apply(this,e):r;++i<n;)o=t[i].call(this,o);return o}}))}function Ni(e,t,n,o,a,s,c,f,l,d){var p=t&u,h=1&t,v=2&t,m=24&t,g=512&t,y=v?i:Mi(e);return function u(){for(var _=arguments.length,C=r(_),b=_;b--;)C[b]=arguments[b];if(m)var w=io(u),S=function(e,t){for(var n=e.length,r=0;n--;)e[n]===t&&++r;return r}(C,w);if(o&&(C=Ti(C,o,a,m)),s&&(C=Ri(C,s,c,m)),_-=S,m&&_<d){var T=sn(C,w);return Ji(e,t,Ni,u.placeholder,n,C,T,f,l,d-_)}var R=h?n:this,E=v?R[e]:e;return _=C.length,f?C=function(e,t){for(var n=e.length,r=gn(t.length,n),o=Ei(e);r--;){var a=t[r];e[r]=mo(a,n)?o[a]:i}return e}(C,f):g&&_>1&&C.reverse(),p&&l<_&&(C.length=l),this&&this!==lt&&this instanceof u&&(E=y||Mi(E)),E.apply(R,C)}}function Ui(e,t){return function(n,r){return function(e,t,n,r){return yr(e,(function(e,i,o){t(r,n(e),i,o)})),r}(n,e,t(r),{})}}function zi(e,t){return function(n,r){var o;if(n===i&&r===i)return t;if(n!==i&&(o=n),r!==i){if(o===i)return r;"string"==typeof n||"string"==typeof r?(n=ai(n),r=ai(r)):(n=oi(n),r=oi(r)),o=e(n,r)}return o}}function Gi(e){return Qi((function(t){return t=It(t,Kt(oo())),Hr((function(n){var r=this;return e(t,(function(e){return St(e,r,n)}))}))}))}function Bi(e,t){var n=(t=t===i?" ":ai(t)).length;if(n<2)return n?$r(t,e):t;var r=$r(t,dt(e/fn(t)));return rn(t)?yi(ln(r),0,e).join(""):r.slice(0,e)}function Fi(e){return function(t,n,o){return o&&"number"!=typeof o&&go(t,n,o)&&(n=o=i),t=ds(t),n===i?(n=t,t=0):n=ds(n),function(e,t,n,i){for(var o=-1,a=mn(dt((t-e)/(n||1)),0),s=r(a);a--;)s[i?a:++o]=e,e+=n;return s}(t,n,o=o===i?t<n?1:-1:ds(o),e)}}function Wi(e){return function(t,n){return"string"==typeof t&&"string"==typeof n||(t=vs(t),n=vs(n)),e(t,n)}}function Ji(e,t,n,r,o,a,s,u,f,l){var d=8&t;t|=d?c:64,4&(t&=~(d?64:c))||(t&=-4);var p=[e,t,o,d?a:i,d?s:i,d?i:a,d?i:s,u,f,l],h=n.apply(i,p);return _o(e)&&Po(h,p),h.placeholder=r,Oo(h,e,t)}function Vi(e){var t=Se[e];return function(e,n){if(e=vs(e),(n=null==n?0:gn(ps(n),292))&&At(e)){var r=(gs(e)+"e").split("e");return+((r=(gs(t(r[0]+"e"+(+r[1]+n)))+"e").split("e"))[0]+"e"+(+r[1]-n))}return t(e)}}var $i=Rn&&1/cn(new Rn([,-0]))[1]==f?function(e){return new Rn(e)}:sc;function Hi(e){return function(t){var n=lo(t);return n==w?on(t):n==P?un(t):function(e,t){return It(t,(function(t){return[t,e[t]]}))}(t,e(t))}}function qi(e,t,n,a,f,l,d,p){var h=2&t;if(!h&&"function"!=typeof e)throw new Pe(o);var v=a?a.length:0;if(v||(t&=-97,a=f=i),d=d===i?d:mn(ps(d),0),p=p===i?p:ps(p),v-=f?f.length:0,64&t){var m=a,g=f;a=f=i}var y=h?i:no(e),_=[e,t,n,a,f,m,g,l,d,p];if(y&&function(e,t){var n=e[1],r=t[1],i=n|r,o=i<131,a=r==u&&8==n||r==u&&256==n&&e[7].length<=t[8]||384==r&&t[7].length<=t[8]&&8==n;if(!o&&!a)return e;1&r&&(e[2]=t[2],i|=1&n?0:4);var c=t[3];if(c){var f=e[3];e[3]=f?Ti(f,c,t[4]):c,e[4]=f?sn(e[3],s):t[4]}(c=t[5])&&(f=e[5],e[5]=f?Ri(f,c,t[6]):c,e[6]=f?sn(e[5],s):t[6]),(c=t[7])&&(e[7]=c),r&u&&(e[8]=null==e[8]?t[8]:gn(e[8],t[8])),null==e[9]&&(e[9]=t[9]),e[0]=t[0],e[1]=i}(_,y),e=_[0],t=_[1],n=_[2],a=_[3],f=_[4],!(p=_[9]=_[9]===i?h?0:e.length:mn(_[9]-v,0))&&24&t&&(t&=-25),t&&1!=t)C=8==t||16==t?function(e,t,n){var o=Mi(e);return function a(){for(var s=arguments.length,c=r(s),u=s,f=io(a);u--;)c[u]=arguments[u];var l=s<3&&c[0]!==f&&c[s-1]!==f?[]:sn(c,f);return(s-=l.length)<n?Ji(e,t,Ni,a.placeholder,i,c,l,i,i,n-s):St(this&&this!==lt&&this instanceof a?o:e,this,c)}}(e,t,p):t!=c&&33!=t||f.length?Ni.apply(i,_):function(e,t,n,i){var o=1&t,a=Mi(e);return function t(){for(var s=-1,c=arguments.length,u=-1,f=i.length,l=r(f+c),d=this&&this!==lt&&this instanceof t?a:e;++u<f;)l[u]=i[u];for(;c--;)l[u++]=arguments[++s];return St(d,o?n:this,l)}}(e,t,n,a);else var C=function(e,t,n){var r=1&t,i=Mi(e);return function t(){return(this&&this!==lt&&this instanceof t?i:e).apply(r?n:this,arguments)}}(e,t,n);return Oo((y?Xr:Po)(C,_),e,t)}function Ki(e,t,n,r){return e===i||za(e,Oe[n])&&!De.call(r,n)?t:e}function Yi(e,t,n,r,o,a){return Qa(e)&&Qa(t)&&(a.set(t,e),zr(e,t,i,Yi,a),a.delete(t)),e}function Xi(e){return rs(e)?i:e}function Zi(e,t,n,r,o,a){var s=1&n,c=e.length,u=t.length;if(c!=u&&!(s&&u>c))return!1;var f=a.get(e),l=a.get(t);if(f&&l)return f==t&&l==e;var d=-1,p=!0,h=2&n?new $n:i;for(a.set(e,t),a.set(t,e);++d<c;){var v=e[d],m=t[d];if(r)var g=s?r(m,v,d,t,e,a):r(v,m,d,e,t,a);if(g!==i){if(g)continue;p=!1;break}if(h){if(!Lt(t,(function(e,t){if(!Xt(h,t)&&(v===e||o(v,e,n,r,a)))return h.push(t)}))){p=!1;break}}else if(v!==m&&!o(v,m,n,r,a)){p=!1;break}}return a.delete(e),a.delete(t),p}function Qi(e){return xo(To(e,i,Jo),e+"")}function eo(e){return wr(e,Os,uo)}function to(e){return wr(e,Is,fo)}var no=kn?function(e){return kn.get(e)}:sc;function ro(e){for(var t=e.name+"",n=xn[t],r=De.call(xn,t)?n.length:0;r--;){var i=n[r],o=i.func;if(null==o||o==e)return i.name}return t}function io(e){return(De.call(Un,"placeholder")?Un:e).placeholder}function oo(){var e=Un.iteratee||rc;return e=e===rc?Dr:e,arguments.length?e(arguments[0],arguments[1]):e}function ao(e,t){var n,r,i=e.__data__;return("string"==(r=typeof(n=t))||"number"==r||"symbol"==r||"boolean"==r?"__proto__"!==n:null===n)?i["string"==typeof t?"string":"hash"]:i.map}function so(e){for(var t=Os(e),n=t.length;n--;){var r=t[n],i=e[r];t[n]=[r,i,wo(i)]}return t}function co(e,t){var n=function(e,t){return null==e?i:e[t]}(e,t);return jr(n)?n:i}var uo=vt?function(e){return null==e?[]:(e=Te(e),kt(vt(e),(function(t){return $e.call(e,t)})))}:hc,fo=vt?function(e){for(var t=[];e;)jt(t,uo(e)),e=Je(e);return t}:hc,lo=Sr;function po(e,t,n){for(var r=-1,i=(t=mi(t,e)).length,o=!1;++r<i;){var a=Ao(t[r]);if(!(o=null!=e&&n(e,a)))break;e=e[a]}return o||++r!=i?o:!!(i=null==e?0:e.length)&&Za(i)&&mo(a,i)&&(Wa(e)||Fa(e))}function ho(e){return"function"!=typeof e.constructor||bo(e)?{}:zn(Je(e))}function vo(e){return Wa(e)||Fa(e)||!!(qe&&e&&e[qe])}function mo(e,t){var n=typeof e;return!!(t=null==t?l:t)&&("number"==n||"symbol"!=n&&ge.test(e))&&e>-1&&e%1==0&&e<t}function go(e,t,n){if(!Qa(n))return!1;var r=typeof t;return!!("number"==r?Va(n)&&mo(t,n.length):"string"==r&&t in n)&&za(n[t],e)}function yo(e,t){if(Wa(e))return!1;var n=typeof e;return!("number"!=n&&"symbol"!=n&&"boolean"!=n&&null!=e&&!ss(e))||Q.test(e)||!Z.test(e)||null!=t&&e in Te(t)}function _o(e){var t=ro(e),n=Un[t];if("function"!=typeof n||!(t in Fn.prototype))return!1;if(e===n)return!0;var r=no(n);return!!r&&e===r[0]}(wn&&lo(new wn(new ArrayBuffer(1)))!=j||Sn&&lo(new Sn)!=w||Tn&&lo(Tn.resolve())!=R||Rn&&lo(new Rn)!=P||En&&lo(new En)!=O)&&(lo=function(e){var t=Sr(e),n=t==T?e.constructor:i,r=n?No(n):"";if(r)switch(r){case On:return j;case In:return w;case jn:return R;case Dn:return P;case Mn:return O}return t});var Co=Ie?Ya:vc;function bo(e){var t=e&&e.constructor;return e===("function"==typeof t&&t.prototype||Oe)}function wo(e){return e==e&&!Qa(e)}function So(e,t){return function(n){return null!=n&&n[e]===t&&(t!==i||e in Te(n))}}function To(e,t,n){return t=mn(t===i?e.length-1:t,0),function(){for(var i=arguments,o=-1,a=mn(i.length-t,0),s=r(a);++o<a;)s[o]=i[t+o];o=-1;for(var c=r(t+1);++o<t;)c[o]=i[o];return c[t]=n(s),St(e,this,c)}}function Ro(e,t){return t.length<2?e:br(e,ei(t,0,-1))}function Eo(e,t){if(("constructor"!==t||"function"!=typeof e[t])&&"__proto__"!=t)return e[t]}var Po=Io(Xr),ko=ft||function(e,t){return lt.setTimeout(e,t)},xo=Io(Zr);function Oo(e,t,n){var r=t+"";return xo(e,function(e,t){var n=t.length;if(!n)return e;var r=n-1;return t[r]=(n>1?"& ":"")+t[r],t=t.join(n>2?", ":" "),e.replace(oe,"{\n/* [wrapped with "+t+"] */\n")}(r,function(e,t){return Rt(h,(function(n){var r="_."+n[0];t&n[1]&&!xt(e,r)&&e.push(r)})),e.sort()}(function(e){var t=e.match(ae);return t?t[1].split(se):[]}(r),n)))}function Io(e){var t=0,n=0;return function(){var r=yn(),o=16-(r-n);if(n=r,o>0){if(++t>=800)return arguments[0]}else t=0;return e.apply(i,arguments)}}function jo(e,t){var n=-1,r=e.length,o=r-1;for(t=t===i?r:t;++n<t;){var a=Vr(n,o),s=e[a];e[a]=e[n],e[n]=s}return e.length=t,e}var Do,Mo,Lo=(Do=Da((function(e){var t=[];return 46===e.charCodeAt(0)&&t.push(""),e.replace(ee,(function(e,n,r,i){t.push(r?i.replace(fe,"$1"):n||e)})),t}),(function(e){return 500===Mo.size&&Mo.clear(),e})),Mo=Do.cache,Do);function Ao(e){if("string"==typeof e||ss(e))return e;var t=e+"";return"0"==t&&1/e==-1/0?"-0":t}function No(e){if(null!=e){try{return je.call(e)}catch(e){}try{return e+""}catch(e){}}return""}function Uo(e){if(e instanceof Fn)return e.clone();var t=new Bn(e.__wrapped__,e.__chain__);return t.__actions__=Ei(e.__actions__),t.__index__=e.__index__,t.__values__=e.__values__,t}var zo=Hr((function(e,t){return $a(e)?ur(e,vr(t,1,$a,!0)):[]})),Go=Hr((function(e,t){var n=Ko(t);return $a(n)&&(n=i),$a(e)?ur(e,vr(t,1,$a,!0),oo(n,2)):[]})),Bo=Hr((function(e,t){var n=Ko(t);return $a(n)&&(n=i),$a(e)?ur(e,vr(t,1,$a,!0),i,n):[]}));function Fo(e,t,n){var r=null==e?0:e.length;if(!r)return-1;var i=null==n?0:ps(n);return i<0&&(i=mn(r+i,0)),Ut(e,oo(t,3),i)}function Wo(e,t,n){var r=null==e?0:e.length;if(!r)return-1;var o=r-1;return n!==i&&(o=ps(n),o=n<0?mn(r+o,0):gn(o,r-1)),Ut(e,oo(t,3),o,!0)}function Jo(e){return null!=e&&e.length?vr(e,1):[]}function Vo(e){return e&&e.length?e[0]:i}var $o=Hr((function(e){var t=It(e,hi);return t.length&&t[0]===e[0]?Pr(t):[]})),Ho=Hr((function(e){var t=Ko(e),n=It(e,hi);return t===Ko(n)?t=i:n.pop(),n.length&&n[0]===e[0]?Pr(n,oo(t,2)):[]})),qo=Hr((function(e){var t=Ko(e),n=It(e,hi);return(t="function"==typeof t?t:i)&&n.pop(),n.length&&n[0]===e[0]?Pr(n,i,t):[]}));function Ko(e){var t=null==e?0:e.length;return t?e[t-1]:i}var Yo=Hr(Xo);function Xo(e,t){return e&&e.length&&t&&t.length?Wr(e,t):e}var Zo=Qi((function(e,t){var n=null==e?0:e.length,r=ir(e,t);return Jr(e,It(t,(function(e){return mo(e,n)?+e:e})).sort(Si)),r}));function Qo(e){return null==e?e:bn.call(e)}var ea=Hr((function(e){return si(vr(e,1,$a,!0))})),ta=Hr((function(e){var t=Ko(e);return $a(t)&&(t=i),si(vr(e,1,$a,!0),oo(t,2))})),na=Hr((function(e){var t=Ko(e);return t="function"==typeof t?t:i,si(vr(e,1,$a,!0),i,t)}));function ra(e){if(!e||!e.length)return[];var t=0;return e=kt(e,(function(e){if($a(e))return t=mn(e.length,t),!0})),Ht(t,(function(t){return It(e,Wt(t))}))}function ia(e,t){if(!e||!e.length)return[];var n=ra(e);return null==t?n:It(n,(function(e){return St(t,i,e)}))}var oa=Hr((function(e,t){return $a(e)?ur(e,t):[]})),aa=Hr((function(e){return di(kt(e,$a))})),sa=Hr((function(e){var t=Ko(e);return $a(t)&&(t=i),di(kt(e,$a),oo(t,2))})),ca=Hr((function(e){var t=Ko(e);return t="function"==typeof t?t:i,di(kt(e,$a),i,t)})),ua=Hr(ra),fa=Hr((function(e){var t=e.length,n=t>1?e[t-1]:i;return n="function"==typeof n?(e.pop(),n):i,ia(e,n)}));function la(e){var t=Un(e);return t.__chain__=!0,t}function da(e,t){return t(e)}var pa=Qi((function(e){var t=e.length,n=t?e[0]:0,r=this.__wrapped__,o=function(t){return ir(t,e)};return!(t>1||this.__actions__.length)&&r instanceof Fn&&mo(n)?((r=r.slice(n,+n+(t?1:0))).__actions__.push({func:da,args:[o],thisArg:i}),new Bn(r,this.__chain__).thru((function(e){return t&&!e.length&&e.push(i),e}))):this.thru(o)})),ha=ki((function(e,t,n){De.call(e,n)?++e[n]:rr(e,n,1)})),va=Li(Fo),ma=Li(Wo);function ga(e,t){return(Wa(e)?Rt:fr)(e,oo(t,3))}function ya(e,t){return(Wa(e)?Et:lr)(e,oo(t,3))}var _a=ki((function(e,t,n){De.call(e,n)?e[n].push(t):rr(e,n,[t])})),Ca=Hr((function(e,t,n){var i=-1,o="function"==typeof t,a=Va(e)?r(e.length):[];return fr(e,(function(e){a[++i]=o?St(t,e,n):kr(e,t,n)})),a})),ba=ki((function(e,t,n){rr(e,n,t)}));function wa(e,t){return(Wa(e)?It:Ar)(e,oo(t,3))}var Sa=ki((function(e,t,n){e[n?0:1].push(t)}),(function(){return[[],[]]})),Ta=Hr((function(e,t){if(null==e)return[];var n=t.length;return n>1&&go(e,t[0],t[1])?t=[]:n>2&&go(t[0],t[1],t[2])&&(t=[t[0]]),Br(e,vr(t,1),[])})),Ra=ut||function(){return lt.Date.now()};function Ea(e,t,n){return t=n?i:t,t=e&&null==t?e.length:t,qi(e,u,i,i,i,i,t)}function Pa(e,t){var n;if("function"!=typeof t)throw new Pe(o);return e=ps(e),function(){return--e>0&&(n=t.apply(this,arguments)),e<=1&&(t=i),n}}var ka=Hr((function(e,t,n){var r=1;if(n.length){var i=sn(n,io(ka));r|=c}return qi(e,r,t,n,i)})),xa=Hr((function(e,t,n){var r=3;if(n.length){var i=sn(n,io(xa));r|=c}return qi(t,r,e,n,i)}));function Oa(e,t,n){var r,a,s,c,u,f,l=0,d=!1,p=!1,h=!0;if("function"!=typeof e)throw new Pe(o);function v(t){var n=r,o=a;return r=a=i,l=t,c=e.apply(o,n)}function m(e){var n=e-f;return f===i||n>=t||n<0||p&&e-l>=s}function g(){var e=Ra();if(m(e))return y(e);u=ko(g,function(e){var n=t-(e-f);return p?gn(n,s-(e-l)):n}(e))}function y(e){return u=i,h&&r?v(e):(r=a=i,c)}function _(){var e=Ra(),n=m(e);if(r=arguments,a=this,f=e,n){if(u===i)return function(e){return l=e,u=ko(g,t),d?v(e):c}(f);if(p)return _i(u),u=ko(g,t),v(f)}return u===i&&(u=ko(g,t)),c}return t=vs(t)||0,Qa(n)&&(d=!!n.leading,s=(p="maxWait"in n)?mn(vs(n.maxWait)||0,t):s,h="trailing"in n?!!n.trailing:h),_.cancel=function(){u!==i&&_i(u),l=0,r=f=a=u=i},_.flush=function(){return u===i?c:y(Ra())},_}var Ia=Hr((function(e,t){return cr(e,1,t)})),ja=Hr((function(e,t,n){return cr(e,vs(t)||0,n)}));function Da(e,t){if("function"!=typeof e||null!=t&&"function"!=typeof t)throw new Pe(o);var n=function(){var r=arguments,i=t?t.apply(this,r):r[0],o=n.cache;if(o.has(i))return o.get(i);var a=e.apply(this,r);return n.cache=o.set(i,a)||o,a};return n.cache=new(Da.Cache||Vn),n}function Ma(e){if("function"!=typeof e)throw new Pe(o);return function(){var t=arguments;switch(t.length){case 0:return!e.call(this);case 1:return!e.call(this,t[0]);case 2:return!e.call(this,t[0],t[1]);case 3:return!e.call(this,t[0],t[1],t[2])}return!e.apply(this,t)}}Da.Cache=Vn;var La=gi((function(e,t){var n=(t=1==t.length&&Wa(t[0])?It(t[0],Kt(oo())):It(vr(t,1),Kt(oo()))).length;return Hr((function(r){for(var i=-1,o=gn(r.length,n);++i<o;)r[i]=t[i].call(this,r[i]);return St(e,this,r)}))})),Aa=Hr((function(e,t){var n=sn(t,io(Aa));return qi(e,c,i,t,n)})),Na=Hr((function(e,t){var n=sn(t,io(Na));return qi(e,64,i,t,n)})),Ua=Qi((function(e,t){return qi(e,256,i,i,i,t)}));function za(e,t){return e===t||e!=e&&t!=t}var Ga=Wi(Tr),Ba=Wi((function(e,t){return e>=t})),Fa=xr(function(){return arguments}())?xr:function(e){return es(e)&&De.call(e,"callee")&&!$e.call(e,"callee")},Wa=r.isArray,Ja=gt?Kt(gt):function(e){return es(e)&&Sr(e)==I};function Va(e){return null!=e&&Za(e.length)&&!Ya(e)}function $a(e){return es(e)&&Va(e)}var Ha=mt||vc,qa=yt?Kt(yt):function(e){return es(e)&&Sr(e)==y};function Ka(e){if(!es(e))return!1;var t=Sr(e);return t==_||"[object DOMException]"==t||"string"==typeof e.message&&"string"==typeof e.name&&!rs(e)}function Ya(e){if(!Qa(e))return!1;var t=Sr(e);return t==C||t==b||"[object AsyncFunction]"==t||"[object Proxy]"==t}function Xa(e){return"number"==typeof e&&e==ps(e)}function Za(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=l}function Qa(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)}function es(e){return null!=e&&"object"==typeof e}var ts=_t?Kt(_t):function(e){return es(e)&&lo(e)==w};function ns(e){return"number"==typeof e||es(e)&&Sr(e)==S}function rs(e){if(!es(e)||Sr(e)!=T)return!1;var t=Je(e);if(null===t)return!0;var n=De.call(t,"constructor")&&t.constructor;return"function"==typeof n&&n instanceof n&&je.call(n)==Ne}var is=Ct?Kt(Ct):function(e){return es(e)&&Sr(e)==E},os=bt?Kt(bt):function(e){return es(e)&&lo(e)==P};function as(e){return"string"==typeof e||!Wa(e)&&es(e)&&Sr(e)==k}function ss(e){return"symbol"==typeof e||es(e)&&Sr(e)==x}var cs=wt?Kt(wt):function(e){return es(e)&&Za(e.length)&&!!it[Sr(e)]},us=Wi(Lr),fs=Wi((function(e,t){return e<=t}));function ls(e){if(!e)return[];if(Va(e))return as(e)?ln(e):Ei(e);if(Ke&&e[Ke])return function(e){for(var t,n=[];!(t=e.next()).done;)n.push(t.value);return n}(e[Ke]());var t=lo(e);return(t==w?on:t==P?cn:zs)(e)}function ds(e){return e?(e=vs(e))===f||e===-1/0?17976931348623157e292*(e<0?-1:1):e==e?e:0:0===e?e:0}function ps(e){var t=ds(e),n=t%1;return t==t?n?t-n:t:0}function hs(e){return e?or(ps(e),0,p):0}function vs(e){if("number"==typeof e)return e;if(ss(e))return d;if(Qa(e)){var t="function"==typeof e.valueOf?e.valueOf():e;e=Qa(t)?t+"":t}if("string"!=typeof e)return 0===e?e:+e;e=qt(e);var n=he.test(e);return n||me.test(e)?ct(e.slice(2),n?2:8):pe.test(e)?d:+e}function ms(e){return Pi(e,Is(e))}function gs(e){return null==e?"":ai(e)}var ys=xi((function(e,t){if(bo(t)||Va(t))Pi(t,Os(t),e);else for(var n in t)De.call(t,n)&&Qn(e,n,t[n])})),_s=xi((function(e,t){Pi(t,Is(t),e)})),Cs=xi((function(e,t,n,r){Pi(t,Is(t),e,r)})),bs=xi((function(e,t,n,r){Pi(t,Os(t),e,r)})),ws=Qi(ir),Ss=Hr((function(e,t){e=Te(e);var n=-1,r=t.length,o=r>2?t[2]:i;for(o&&go(t[0],t[1],o)&&(r=1);++n<r;)for(var a=t[n],s=Is(a),c=-1,u=s.length;++c<u;){var f=s[c],l=e[f];(l===i||za(l,Oe[f])&&!De.call(e,f))&&(e[f]=a[f])}return e})),Ts=Hr((function(e){return e.push(i,Yi),St(Ds,i,e)}));function Rs(e,t,n){var r=null==e?i:br(e,t);return r===i?n:r}function Es(e,t){return null!=e&&po(e,t,Er)}var Ps=Ui((function(e,t,n){null!=t&&"function"!=typeof t.toString&&(t=Ae.call(t)),e[t]=n}),Qs(nc)),ks=Ui((function(e,t,n){null!=t&&"function"!=typeof t.toString&&(t=Ae.call(t)),De.call(e,t)?e[t].push(n):e[t]=[n]}),oo),xs=Hr(kr);function Os(e){return Va(e)?qn(e):Mr(e)}function Is(e){return Va(e)?qn(e,!0):function(e){if(!Qa(e))return function(e){var t=[];if(null!=e)for(var n in Te(e))t.push(n);return t}(e);var t=bo(e),n=[];for(var r in e)("constructor"!=r||!t&&De.call(e,r))&&n.push(r);return n}(e)}var js=xi((function(e,t,n){zr(e,t,n)})),Ds=xi((function(e,t,n,r){zr(e,t,n,r)})),Ms=Qi((function(e,t){var n={};if(null==e)return n;var r=!1;t=It(t,(function(t){return t=mi(t,e),r||(r=t.length>1),t})),Pi(e,to(e),n),r&&(n=ar(n,7,Xi));for(var i=t.length;i--;)ci(n,t[i]);return n})),Ls=Qi((function(e,t){return null==e?{}:function(e,t){return Fr(e,t,(function(t,n){return Es(e,n)}))}(e,t)}));function As(e,t){if(null==e)return{};var n=It(to(e),(function(e){return[e]}));return t=oo(t),Fr(e,n,(function(e,n){return t(e,n[0])}))}var Ns=Hi(Os),Us=Hi(Is);function zs(e){return null==e?[]:Yt(e,Os(e))}var Gs=Di((function(e,t,n){return t=t.toLowerCase(),e+(n?Bs(t):t)}));function Bs(e){return Ks(gs(e).toLowerCase())}function Fs(e){return(e=gs(e))&&e.replace(ye,en).replace(Xe,"")}var Ws=Di((function(e,t,n){return e+(n?"-":"")+t.toLowerCase()})),Js=Di((function(e,t,n){return e+(n?" ":"")+t.toLowerCase()})),Vs=ji("toLowerCase"),$s=Di((function(e,t,n){return e+(n?"_":"")+t.toLowerCase()})),Hs=Di((function(e,t,n){return e+(n?" ":"")+Ks(t)})),qs=Di((function(e,t,n){return e+(n?" ":"")+t.toUpperCase()})),Ks=ji("toUpperCase");function Ys(e,t,n){return e=gs(e),(t=n?i:t)===i?function(e){return tt.test(e)}(e)?function(e){return e.match(Qe)||[]}(e):function(e){return e.match(ce)||[]}(e):e.match(t)||[]}var Xs=Hr((function(e,t){try{return St(e,i,t)}catch(e){return Ka(e)?e:new be(e)}})),Zs=Qi((function(e,t){return Rt(t,(function(t){t=Ao(t),rr(e,t,ka(e[t],e))})),e}));function Qs(e){return function(){return e}}var ec=Ai(),tc=Ai(!0);function nc(e){return e}function rc(e){return Dr("function"==typeof e?e:ar(e,1))}var ic=Hr((function(e,t){return function(n){return kr(n,e,t)}})),oc=Hr((function(e,t){return function(n){return kr(e,n,t)}}));function ac(e,t,n){var r=Os(t),i=Cr(t,r);null!=n||Qa(t)&&(i.length||!r.length)||(n=t,t=e,e=this,i=Cr(t,Os(t)));var o=!(Qa(n)&&"chain"in n&&!n.chain),a=Ya(e);return Rt(i,(function(n){var r=t[n];e[n]=r,a&&(e.prototype[n]=function(){var t=this.__chain__;if(o||t){var n=e(this.__wrapped__);return(n.__actions__=Ei(this.__actions__)).push({func:r,args:arguments,thisArg:e}),n.__chain__=t,n}return r.apply(e,jt([this.value()],arguments))})})),e}function sc(){}var cc=Gi(It),uc=Gi(Pt),fc=Gi(Lt);function lc(e){return yo(e)?Wt(Ao(e)):function(e){return function(t){return br(t,e)}}(e)}var dc=Fi(),pc=Fi(!0);function hc(){return[]}function vc(){return!1}var mc,gc=zi((function(e,t){return e+t}),0),yc=Vi("ceil"),_c=zi((function(e,t){return e/t}),1),Cc=Vi("floor"),bc=zi((function(e,t){return e*t}),1),wc=Vi("round"),Sc=zi((function(e,t){return e-t}),0);return Un.after=function(e,t){if("function"!=typeof t)throw new Pe(o);return e=ps(e),function(){if(--e<1)return t.apply(this,arguments)}},Un.ary=Ea,Un.assign=ys,Un.assignIn=_s,Un.assignInWith=Cs,Un.assignWith=bs,Un.at=ws,Un.before=Pa,Un.bind=ka,Un.bindAll=Zs,Un.bindKey=xa,Un.castArray=function(){if(!arguments.length)return[];var e=arguments[0];return Wa(e)?e:[e]},Un.chain=la,Un.chunk=function(e,t,n){t=(n?go(e,t,n):t===i)?1:mn(ps(t),0);var o=null==e?0:e.length;if(!o||t<1)return[];for(var a=0,s=0,c=r(dt(o/t));a<o;)c[s++]=ei(e,a,a+=t);return c},Un.compact=function(e){for(var t=-1,n=null==e?0:e.length,r=0,i=[];++t<n;){var o=e[t];o&&(i[r++]=o)}return i},Un.concat=function(){var e=arguments.length;if(!e)return[];for(var t=r(e-1),n=arguments[0],i=e;i--;)t[i-1]=arguments[i];return jt(Wa(n)?Ei(n):[n],vr(t,1))},Un.cond=function(e){var t=null==e?0:e.length,n=oo();return e=t?It(e,(function(e){if("function"!=typeof e[1])throw new Pe(o);return[n(e[0]),e[1]]})):[],Hr((function(n){for(var r=-1;++r<t;){var i=e[r];if(St(i[0],this,n))return St(i[1],this,n)}}))},Un.conforms=function(e){return function(e){var t=Os(e);return function(n){return sr(n,e,t)}}(ar(e,1))},Un.constant=Qs,Un.countBy=ha,Un.create=function(e,t){var n=zn(e);return null==t?n:nr(n,t)},Un.curry=function e(t,n,r){var o=qi(t,8,i,i,i,i,i,n=r?i:n);return o.placeholder=e.placeholder,o},Un.curryRight=function e(t,n,r){var o=qi(t,16,i,i,i,i,i,n=r?i:n);return o.placeholder=e.placeholder,o},Un.debounce=Oa,Un.defaults=Ss,Un.defaultsDeep=Ts,Un.defer=Ia,Un.delay=ja,Un.difference=zo,Un.differenceBy=Go,Un.differenceWith=Bo,Un.drop=function(e,t,n){var r=null==e?0:e.length;return r?ei(e,(t=n||t===i?1:ps(t))<0?0:t,r):[]},Un.dropRight=function(e,t,n){var r=null==e?0:e.length;return r?ei(e,0,(t=r-(t=n||t===i?1:ps(t)))<0?0:t):[]},Un.dropRightWhile=function(e,t){return e&&e.length?fi(e,oo(t,3),!0,!0):[]},Un.dropWhile=function(e,t){return e&&e.length?fi(e,oo(t,3),!0):[]},Un.fill=function(e,t,n,r){var o=null==e?0:e.length;return o?(n&&"number"!=typeof n&&go(e,t,n)&&(n=0,r=o),function(e,t,n,r){var o=e.length;for((n=ps(n))<0&&(n=-n>o?0:o+n),(r=r===i||r>o?o:ps(r))<0&&(r+=o),r=n>r?0:hs(r);n<r;)e[n++]=t;return e}(e,t,n,r)):[]},Un.filter=function(e,t){return(Wa(e)?kt:hr)(e,oo(t,3))},Un.flatMap=function(e,t){return vr(wa(e,t),1)},Un.flatMapDeep=function(e,t){return vr(wa(e,t),f)},Un.flatMapDepth=function(e,t,n){return n=n===i?1:ps(n),vr(wa(e,t),n)},Un.flatten=Jo,Un.flattenDeep=function(e){return null!=e&&e.length?vr(e,f):[]},Un.flattenDepth=function(e,t){return null!=e&&e.length?vr(e,t=t===i?1:ps(t)):[]},Un.flip=function(e){return qi(e,512)},Un.flow=ec,Un.flowRight=tc,Un.fromPairs=function(e){for(var t=-1,n=null==e?0:e.length,r={};++t<n;){var i=e[t];r[i[0]]=i[1]}return r},Un.functions=function(e){return null==e?[]:Cr(e,Os(e))},Un.functionsIn=function(e){return null==e?[]:Cr(e,Is(e))},Un.groupBy=_a,Un.initial=function(e){return null!=e&&e.length?ei(e,0,-1):[]},Un.intersection=$o,Un.intersectionBy=Ho,Un.intersectionWith=qo,Un.invert=Ps,Un.invertBy=ks,Un.invokeMap=Ca,Un.iteratee=rc,Un.keyBy=ba,Un.keys=Os,Un.keysIn=Is,Un.map=wa,Un.mapKeys=function(e,t){var n={};return t=oo(t,3),yr(e,(function(e,r,i){rr(n,t(e,r,i),e)})),n},Un.mapValues=function(e,t){var n={};return t=oo(t,3),yr(e,(function(e,r,i){rr(n,r,t(e,r,i))})),n},Un.matches=function(e){return Nr(ar(e,1))},Un.matchesProperty=function(e,t){return Ur(e,ar(t,1))},Un.memoize=Da,Un.merge=js,Un.mergeWith=Ds,Un.method=ic,Un.methodOf=oc,Un.mixin=ac,Un.negate=Ma,Un.nthArg=function(e){return e=ps(e),Hr((function(t){return Gr(t,e)}))},Un.omit=Ms,Un.omitBy=function(e,t){return As(e,Ma(oo(t)))},Un.once=function(e){return Pa(2,e)},Un.orderBy=function(e,t,n,r){return null==e?[]:(Wa(t)||(t=null==t?[]:[t]),Wa(n=r?i:n)||(n=null==n?[]:[n]),Br(e,t,n))},Un.over=cc,Un.overArgs=La,Un.overEvery=uc,Un.overSome=fc,Un.partial=Aa,Un.partialRight=Na,Un.partition=Sa,Un.pick=Ls,Un.pickBy=As,Un.property=lc,Un.propertyOf=function(e){return function(t){return null==e?i:br(e,t)}},Un.pull=Yo,Un.pullAll=Xo,Un.pullAllBy=function(e,t,n){return e&&e.length&&t&&t.length?Wr(e,t,oo(n,2)):e},Un.pullAllWith=function(e,t,n){return e&&e.length&&t&&t.length?Wr(e,t,i,n):e},Un.pullAt=Zo,Un.range=dc,Un.rangeRight=pc,Un.rearg=Ua,Un.reject=function(e,t){return(Wa(e)?kt:hr)(e,Ma(oo(t,3)))},Un.remove=function(e,t){var n=[];if(!e||!e.length)return n;var r=-1,i=[],o=e.length;for(t=oo(t,3);++r<o;){var a=e[r];t(a,r,e)&&(n.push(a),i.push(r))}return Jr(e,i),n},Un.rest=function(e,t){if("function"!=typeof e)throw new Pe(o);return Hr(e,t=t===i?t:ps(t))},Un.reverse=Qo,Un.sampleSize=function(e,t,n){return t=(n?go(e,t,n):t===i)?1:ps(t),(Wa(e)?Yn:Kr)(e,t)},Un.set=function(e,t,n){return null==e?e:Yr(e,t,n)},Un.setWith=function(e,t,n,r){return r="function"==typeof r?r:i,null==e?e:Yr(e,t,n,r)},Un.shuffle=function(e){return(Wa(e)?Xn:Qr)(e)},Un.slice=function(e,t,n){var r=null==e?0:e.length;return r?(n&&"number"!=typeof n&&go(e,t,n)?(t=0,n=r):(t=null==t?0:ps(t),n=n===i?r:ps(n)),ei(e,t,n)):[]},Un.sortBy=Ta,Un.sortedUniq=function(e){return e&&e.length?ii(e):[]},Un.sortedUniqBy=function(e,t){return e&&e.length?ii(e,oo(t,2)):[]},Un.split=function(e,t,n){return n&&"number"!=typeof n&&go(e,t,n)&&(t=n=i),(n=n===i?p:n>>>0)?(e=gs(e))&&("string"==typeof t||null!=t&&!is(t))&&!(t=ai(t))&&rn(e)?yi(ln(e),0,n):e.split(t,n):[]},Un.spread=function(e,t){if("function"!=typeof e)throw new Pe(o);return t=null==t?0:mn(ps(t),0),Hr((function(n){var r=n[t],i=yi(n,0,t);return r&&jt(i,r),St(e,this,i)}))},Un.tail=function(e){var t=null==e?0:e.length;return t?ei(e,1,t):[]},Un.take=function(e,t,n){return e&&e.length?ei(e,0,(t=n||t===i?1:ps(t))<0?0:t):[]},Un.takeRight=function(e,t,n){var r=null==e?0:e.length;return r?ei(e,(t=r-(t=n||t===i?1:ps(t)))<0?0:t,r):[]},Un.takeRightWhile=function(e,t){return e&&e.length?fi(e,oo(t,3),!1,!0):[]},Un.takeWhile=function(e,t){return e&&e.length?fi(e,oo(t,3)):[]},Un.tap=function(e,t){return t(e),e},Un.throttle=function(e,t,n){var r=!0,i=!0;if("function"!=typeof e)throw new Pe(o);return Qa(n)&&(r="leading"in n?!!n.leading:r,i="trailing"in n?!!n.trailing:i),Oa(e,t,{leading:r,maxWait:t,trailing:i})},Un.thru=da,Un.toArray=ls,Un.toPairs=Ns,Un.toPairsIn=Us,Un.toPath=function(e){return Wa(e)?It(e,Ao):ss(e)?[e]:Ei(Lo(gs(e)))},Un.toPlainObject=ms,Un.transform=function(e,t,n){var r=Wa(e),i=r||Ha(e)||cs(e);if(t=oo(t,4),null==n){var o=e&&e.constructor;n=i?r?new o:[]:Qa(e)&&Ya(o)?zn(Je(e)):{}}return(i?Rt:yr)(e,(function(e,r,i){return t(n,e,r,i)})),n},Un.unary=function(e){return Ea(e,1)},Un.union=ea,Un.unionBy=ta,Un.unionWith=na,Un.uniq=function(e){return e&&e.length?si(e):[]},Un.uniqBy=function(e,t){return e&&e.length?si(e,oo(t,2)):[]},Un.uniqWith=function(e,t){return t="function"==typeof t?t:i,e&&e.length?si(e,i,t):[]},Un.unset=function(e,t){return null==e||ci(e,t)},Un.unzip=ra,Un.unzipWith=ia,Un.update=function(e,t,n){return null==e?e:ui(e,t,vi(n))},Un.updateWith=function(e,t,n,r){return r="function"==typeof r?r:i,null==e?e:ui(e,t,vi(n),r)},Un.values=zs,Un.valuesIn=function(e){return null==e?[]:Yt(e,Is(e))},Un.without=oa,Un.words=Ys,Un.wrap=function(e,t){return Aa(vi(t),e)},Un.xor=aa,Un.xorBy=sa,Un.xorWith=ca,Un.zip=ua,Un.zipObject=function(e,t){return pi(e||[],t||[],Qn)},Un.zipObjectDeep=function(e,t){return pi(e||[],t||[],Yr)},Un.zipWith=fa,Un.entries=Ns,Un.entriesIn=Us,Un.extend=_s,Un.extendWith=Cs,ac(Un,Un),Un.add=gc,Un.attempt=Xs,Un.camelCase=Gs,Un.capitalize=Bs,Un.ceil=yc,Un.clamp=function(e,t,n){return n===i&&(n=t,t=i),n!==i&&(n=(n=vs(n))==n?n:0),t!==i&&(t=(t=vs(t))==t?t:0),or(vs(e),t,n)},Un.clone=function(e){return ar(e,4)},Un.cloneDeep=function(e){return ar(e,5)},Un.cloneDeepWith=function(e,t){return ar(e,5,t="function"==typeof t?t:i)},Un.cloneWith=function(e,t){return ar(e,4,t="function"==typeof t?t:i)},Un.conformsTo=function(e,t){return null==t||sr(e,t,Os(t))},Un.deburr=Fs,Un.defaultTo=function(e,t){return null==e||e!=e?t:e},Un.divide=_c,Un.endsWith=function(e,t,n){e=gs(e),t=ai(t);var r=e.length,o=n=n===i?r:or(ps(n),0,r);return(n-=t.length)>=0&&e.slice(n,o)==t},Un.eq=za,Un.escape=function(e){return(e=gs(e))&&q.test(e)?e.replace($,tn):e},Un.escapeRegExp=function(e){return(e=gs(e))&&ne.test(e)?e.replace(te,"\\$&"):e},Un.every=function(e,t,n){var r=Wa(e)?Pt:dr;return n&&go(e,t,n)&&(t=i),r(e,oo(t,3))},Un.find=va,Un.findIndex=Fo,Un.findKey=function(e,t){return Nt(e,oo(t,3),yr)},Un.findLast=ma,Un.findLastIndex=Wo,Un.findLastKey=function(e,t){return Nt(e,oo(t,3),_r)},Un.floor=Cc,Un.forEach=ga,Un.forEachRight=ya,Un.forIn=function(e,t){return null==e?e:mr(e,oo(t,3),Is)},Un.forInRight=function(e,t){return null==e?e:gr(e,oo(t,3),Is)},Un.forOwn=function(e,t){return e&&yr(e,oo(t,3))},Un.forOwnRight=function(e,t){return e&&_r(e,oo(t,3))},Un.get=Rs,Un.gt=Ga,Un.gte=Ba,Un.has=function(e,t){return null!=e&&po(e,t,Rr)},Un.hasIn=Es,Un.head=Vo,Un.identity=nc,Un.includes=function(e,t,n,r){e=Va(e)?e:zs(e),n=n&&!r?ps(n):0;var i=e.length;return n<0&&(n=mn(i+n,0)),as(e)?n<=i&&e.indexOf(t,n)>-1:!!i&&zt(e,t,n)>-1},Un.indexOf=function(e,t,n){var r=null==e?0:e.length;if(!r)return-1;var i=null==n?0:ps(n);return i<0&&(i=mn(r+i,0)),zt(e,t,i)},Un.inRange=function(e,t,n){return t=ds(t),n===i?(n=t,t=0):n=ds(n),function(e,t,n){return e>=gn(t,n)&&e<mn(t,n)}(e=vs(e),t,n)},Un.invoke=xs,Un.isArguments=Fa,Un.isArray=Wa,Un.isArrayBuffer=Ja,Un.isArrayLike=Va,Un.isArrayLikeObject=$a,Un.isBoolean=function(e){return!0===e||!1===e||es(e)&&Sr(e)==g},Un.isBuffer=Ha,Un.isDate=qa,Un.isElement=function(e){return es(e)&&1===e.nodeType&&!rs(e)},Un.isEmpty=function(e){if(null==e)return!0;if(Va(e)&&(Wa(e)||"string"==typeof e||"function"==typeof e.splice||Ha(e)||cs(e)||Fa(e)))return!e.length;var t=lo(e);if(t==w||t==P)return!e.size;if(bo(e))return!Mr(e).length;for(var n in e)if(De.call(e,n))return!1;return!0},Un.isEqual=function(e,t){return Or(e,t)},Un.isEqualWith=function(e,t,n){var r=(n="function"==typeof n?n:i)?n(e,t):i;return r===i?Or(e,t,i,n):!!r},Un.isError=Ka,Un.isFinite=function(e){return"number"==typeof e&&At(e)},Un.isFunction=Ya,Un.isInteger=Xa,Un.isLength=Za,Un.isMap=ts,Un.isMatch=function(e,t){return e===t||Ir(e,t,so(t))},Un.isMatchWith=function(e,t,n){return n="function"==typeof n?n:i,Ir(e,t,so(t),n)},Un.isNaN=function(e){return ns(e)&&e!=+e},Un.isNative=function(e){if(Co(e))throw new be("Unsupported core-js use. Try https://npms.io/search?q=ponyfill.");return jr(e)},Un.isNil=function(e){return null==e},Un.isNull=function(e){return null===e},Un.isNumber=ns,Un.isObject=Qa,Un.isObjectLike=es,Un.isPlainObject=rs,Un.isRegExp=is,Un.isSafeInteger=function(e){return Xa(e)&&e>=-9007199254740991&&e<=l},Un.isSet=os,Un.isString=as,Un.isSymbol=ss,Un.isTypedArray=cs,Un.isUndefined=function(e){return e===i},Un.isWeakMap=function(e){return es(e)&&lo(e)==O},Un.isWeakSet=function(e){return es(e)&&"[object WeakSet]"==Sr(e)},Un.join=function(e,t){return null==e?"":Jt.call(e,t)},Un.kebabCase=Ws,Un.last=Ko,Un.lastIndexOf=function(e,t,n){var r=null==e?0:e.length;if(!r)return-1;var o=r;return n!==i&&(o=(o=ps(n))<0?mn(r+o,0):gn(o,r-1)),t==t?function(e,t,n){for(var r=n+1;r--;)if(e[r]===t)return r;return r}(e,t,o):Ut(e,Bt,o,!0)},Un.lowerCase=Js,Un.lowerFirst=Vs,Un.lt=us,Un.lte=fs,Un.max=function(e){return e&&e.length?pr(e,nc,Tr):i},Un.maxBy=function(e,t){return e&&e.length?pr(e,oo(t,2),Tr):i},Un.mean=function(e){return Ft(e,nc)},Un.meanBy=function(e,t){return Ft(e,oo(t,2))},Un.min=function(e){return e&&e.length?pr(e,nc,Lr):i},Un.minBy=function(e,t){return e&&e.length?pr(e,oo(t,2),Lr):i},Un.stubArray=hc,Un.stubFalse=vc,Un.stubObject=function(){return{}},Un.stubString=function(){return""},Un.stubTrue=function(){return!0},Un.multiply=bc,Un.nth=function(e,t){return e&&e.length?Gr(e,ps(t)):i},Un.noConflict=function(){return lt._===this&&(lt._=Ue),this},Un.noop=sc,Un.now=Ra,Un.pad=function(e,t,n){e=gs(e);var r=(t=ps(t))?fn(e):0;if(!t||r>=t)return e;var i=(t-r)/2;return Bi(pt(i),n)+e+Bi(dt(i),n)},Un.padEnd=function(e,t,n){e=gs(e);var r=(t=ps(t))?fn(e):0;return t&&r<t?e+Bi(t-r,n):e},Un.padStart=function(e,t,n){e=gs(e);var r=(t=ps(t))?fn(e):0;return t&&r<t?Bi(t-r,n)+e:e},Un.parseInt=function(e,t,n){return n||null==t?t=0:t&&(t=+t),_n(gs(e).replace(re,""),t||0)},Un.random=function(e,t,n){if(n&&"boolean"!=typeof n&&go(e,t,n)&&(t=n=i),n===i&&("boolean"==typeof t?(n=t,t=i):"boolean"==typeof e&&(n=e,e=i)),e===i&&t===i?(e=0,t=1):(e=ds(e),t===i?(t=e,e=0):t=ds(t)),e>t){var r=e;e=t,t=r}if(n||e%1||t%1){var o=Cn();return gn(e+o*(t-e+st("1e-"+((o+"").length-1))),t)}return Vr(e,t)},Un.reduce=function(e,t,n){var r=Wa(e)?Dt:Vt,i=arguments.length<3;return r(e,oo(t,4),n,i,fr)},Un.reduceRight=function(e,t,n){var r=Wa(e)?Mt:Vt,i=arguments.length<3;return r(e,oo(t,4),n,i,lr)},Un.repeat=function(e,t,n){return t=(n?go(e,t,n):t===i)?1:ps(t),$r(gs(e),t)},Un.replace=function(){var e=arguments,t=gs(e[0]);return e.length<3?t:t.replace(e[1],e[2])},Un.result=function(e,t,n){var r=-1,o=(t=mi(t,e)).length;for(o||(o=1,e=i);++r<o;){var a=null==e?i:e[Ao(t[r])];a===i&&(r=o,a=n),e=Ya(a)?a.call(e):a}return e},Un.round=wc,Un.runInContext=e,Un.sample=function(e){return(Wa(e)?Kn:qr)(e)},Un.size=function(e){if(null==e)return 0;if(Va(e))return as(e)?fn(e):e.length;var t=lo(e);return t==w||t==P?e.size:Mr(e).length},Un.snakeCase=$s,Un.some=function(e,t,n){var r=Wa(e)?Lt:ti;return n&&go(e,t,n)&&(t=i),r(e,oo(t,3))},Un.sortedIndex=function(e,t){return ni(e,t)},Un.sortedIndexBy=function(e,t,n){return ri(e,t,oo(n,2))},Un.sortedIndexOf=function(e,t){var n=null==e?0:e.length;if(n){var r=ni(e,t);if(r<n&&za(e[r],t))return r}return-1},Un.sortedLastIndex=function(e,t){return ni(e,t,!0)},Un.sortedLastIndexBy=function(e,t,n){return ri(e,t,oo(n,2),!0)},Un.sortedLastIndexOf=function(e,t){if(null!=e&&e.length){var n=ni(e,t,!0)-1;if(za(e[n],t))return n}return-1},Un.startCase=Hs,Un.startsWith=function(e,t,n){return e=gs(e),n=null==n?0:or(ps(n),0,e.length),t=ai(t),e.slice(n,n+t.length)==t},Un.subtract=Sc,Un.sum=function(e){return e&&e.length?$t(e,nc):0},Un.sumBy=function(e,t){return e&&e.length?$t(e,oo(t,2)):0},Un.template=function(e,t,n){var r=Un.templateSettings;n&&go(e,t,n)&&(t=i),e=gs(e),t=Cs({},t,r,Ki);var o,a,s=Cs({},t.imports,r.imports,Ki),c=Os(s),u=Yt(s,c),f=0,l=t.interpolate||_e,d="__p += '",p=Re((t.escape||_e).source+"|"+l.source+"|"+(l===X?le:_e).source+"|"+(t.evaluate||_e).source+"|$","g"),h="//# sourceURL="+(De.call(t,"sourceURL")?(t.sourceURL+"").replace(/\s/g," "):"lodash.templateSources["+ ++rt+"]")+"\n";e.replace(p,(function(t,n,r,i,s,c){return r||(r=i),d+=e.slice(f,c).replace(Ce,nn),n&&(o=!0,d+="' +\n__e("+n+") +\n'"),s&&(a=!0,d+="';\n"+s+";\n__p += '"),r&&(d+="' +\n((__t = ("+r+")) == null ? '' : __t) +\n'"),f=c+t.length,t})),d+="';\n";var v=De.call(t,"variable")&&t.variable;if(v){if(ue.test(v))throw new be("Invalid `variable` option passed into `_.template`")}else d="with (obj) {\n"+d+"\n}\n";d=(a?d.replace(F,""):d).replace(W,"$1").replace(J,"$1;"),d="function("+(v||"obj")+") {\n"+(v?"":"obj || (obj = {});\n")+"var __t, __p = ''"+(o?", __e = _.escape":"")+(a?", __j = Array.prototype.join;\nfunction print() { __p += __j.call(arguments, '') }\n":";\n")+d+"return __p\n}";var m=Xs((function(){return we(c,h+"return "+d).apply(i,u)}));if(m.source=d,Ka(m))throw m;return m},Un.times=function(e,t){if((e=ps(e))<1||e>l)return[];var n=p,r=gn(e,p);t=oo(t),e-=p;for(var i=Ht(r,t);++n<e;)t(n);return i},Un.toFinite=ds,Un.toInteger=ps,Un.toLength=hs,Un.toLower=function(e){return gs(e).toLowerCase()},Un.toNumber=vs,Un.toSafeInteger=function(e){return e?or(ps(e),-9007199254740991,l):0===e?e:0},Un.toString=gs,Un.toUpper=function(e){return gs(e).toUpperCase()},Un.trim=function(e,t,n){if((e=gs(e))&&(n||t===i))return qt(e);if(!e||!(t=ai(t)))return e;var r=ln(e),o=ln(t);return yi(r,Zt(r,o),Qt(r,o)+1).join("")},Un.trimEnd=function(e,t,n){if((e=gs(e))&&(n||t===i))return e.slice(0,dn(e)+1);if(!e||!(t=ai(t)))return e;var r=ln(e);return yi(r,0,Qt(r,ln(t))+1).join("")},Un.trimStart=function(e,t,n){if((e=gs(e))&&(n||t===i))return e.replace(re,"");if(!e||!(t=ai(t)))return e;var r=ln(e);return yi(r,Zt(r,ln(t))).join("")},Un.truncate=function(e,t){var n=30,r="...";if(Qa(t)){var o="separator"in t?t.separator:o;n="length"in t?ps(t.length):n,r="omission"in t?ai(t.omission):r}var a=(e=gs(e)).length;if(rn(e)){var s=ln(e);a=s.length}if(n>=a)return e;var c=n-fn(r);if(c<1)return r;var u=s?yi(s,0,c).join(""):e.slice(0,c);if(o===i)return u+r;if(s&&(c+=u.length-c),is(o)){if(e.slice(c).search(o)){var f,l=u;for(o.global||(o=Re(o.source,gs(de.exec(o))+"g")),o.lastIndex=0;f=o.exec(l);)var d=f.index;u=u.slice(0,d===i?c:d)}}else if(e.indexOf(ai(o),c)!=c){var p=u.lastIndexOf(o);p>-1&&(u=u.slice(0,p))}return u+r},Un.unescape=function(e){return(e=gs(e))&&H.test(e)?e.replace(V,pn):e},Un.uniqueId=function(e){var t=++Me;return gs(e)+t},Un.upperCase=qs,Un.upperFirst=Ks,Un.each=ga,Un.eachRight=ya,Un.first=Vo,ac(Un,(mc={},yr(Un,(function(e,t){De.call(Un.prototype,t)||(mc[t]=e)})),mc),{chain:!1}),Un.VERSION="4.17.21",Rt(["bind","bindKey","curry","curryRight","partial","partialRight"],(function(e){Un[e].placeholder=Un})),Rt(["drop","take"],(function(e,t){Fn.prototype[e]=function(n){n=n===i?1:mn(ps(n),0);var r=this.__filtered__&&!t?new Fn(this):this.clone();return r.__filtered__?r.__takeCount__=gn(n,r.__takeCount__):r.__views__.push({size:gn(n,p),type:e+(r.__dir__<0?"Right":"")}),r},Fn.prototype[e+"Right"]=function(t){return this.reverse()[e](t).reverse()}})),Rt(["filter","map","takeWhile"],(function(e,t){var n=t+1,r=1==n||3==n;Fn.prototype[e]=function(e){var t=this.clone();return t.__iteratees__.push({iteratee:oo(e,3),type:n}),t.__filtered__=t.__filtered__||r,t}})),Rt(["head","last"],(function(e,t){var n="take"+(t?"Right":"");Fn.prototype[e]=function(){return this[n](1).value()[0]}})),Rt(["initial","tail"],(function(e,t){var n="drop"+(t?"":"Right");Fn.prototype[e]=function(){return this.__filtered__?new Fn(this):this[n](1)}})),Fn.prototype.compact=function(){return this.filter(nc)},Fn.prototype.find=function(e){return this.filter(e).head()},Fn.prototype.findLast=function(e){return this.reverse().find(e)},Fn.prototype.invokeMap=Hr((function(e,t){return"function"==typeof e?new Fn(this):this.map((function(n){return kr(n,e,t)}))})),Fn.prototype.reject=function(e){return this.filter(Ma(oo(e)))},Fn.prototype.slice=function(e,t){e=ps(e);var n=this;return n.__filtered__&&(e>0||t<0)?new Fn(n):(e<0?n=n.takeRight(-e):e&&(n=n.drop(e)),t!==i&&(n=(t=ps(t))<0?n.dropRight(-t):n.take(t-e)),n)},Fn.prototype.takeRightWhile=function(e){return this.reverse().takeWhile(e).reverse()},Fn.prototype.toArray=function(){return this.take(p)},yr(Fn.prototype,(function(e,t){var n=/^(?:filter|find|map|reject)|While$/.test(t),r=/^(?:head|last)$/.test(t),o=Un[r?"take"+("last"==t?"Right":""):t],a=r||/^find/.test(t);o&&(Un.prototype[t]=function(){var t=this.__wrapped__,s=r?[1]:arguments,c=t instanceof Fn,u=s[0],f=c||Wa(t),l=function(e){var t=o.apply(Un,jt([e],s));return r&&d?t[0]:t};f&&n&&"function"==typeof u&&1!=u.length&&(c=f=!1);var d=this.__chain__,p=!!this.__actions__.length,h=a&&!d,v=c&&!p;if(!a&&f){t=v?t:new Fn(this);var m=e.apply(t,s);return m.__actions__.push({func:da,args:[l],thisArg:i}),new Bn(m,d)}return h&&v?e.apply(this,s):(m=this.thru(l),h?r?m.value()[0]:m.value():m)})})),Rt(["pop","push","shift","sort","splice","unshift"],(function(e){var t=ke[e],n=/^(?:push|sort|unshift)$/.test(e)?"tap":"thru",r=/^(?:pop|shift)$/.test(e);Un.prototype[e]=function(){var e=arguments;if(r&&!this.__chain__){var i=this.value();return t.apply(Wa(i)?i:[],e)}return this[n]((function(n){return t.apply(Wa(n)?n:[],e)}))}})),yr(Fn.prototype,(function(e,t){var n=Un[t];if(n){var r=n.name+"";De.call(xn,r)||(xn[r]=[]),xn[r].push({name:t,func:n})}})),xn[Ni(i,2).name]=[{name:"wrapper",func:i}],Fn.prototype.clone=function(){var e=new Fn(this.__wrapped__);return e.__actions__=Ei(this.__actions__),e.__dir__=this.__dir__,e.__filtered__=this.__filtered__,e.__iteratees__=Ei(this.__iteratees__),e.__takeCount__=this.__takeCount__,e.__views__=Ei(this.__views__),e},Fn.prototype.reverse=function(){if(this.__filtered__){var e=new Fn(this);e.__dir__=-1,e.__filtered__=!0}else(e=this.clone()).__dir__*=-1;return e},Fn.prototype.value=function(){var e=this.__wrapped__.value(),t=this.__dir__,n=Wa(e),r=t<0,i=n?e.length:0,o=function(e,t,n){for(var r=-1,i=n.length;++r<i;){var o=n[r],a=o.size;switch(o.type){case"drop":e+=a;break;case"dropRight":t-=a;break;case"take":t=gn(t,e+a);break;case"takeRight":e=mn(e,t-a)}}return{start:e,end:t}}(0,i,this.__views__),a=o.start,s=o.end,c=s-a,u=r?s:a-1,f=this.__iteratees__,l=f.length,d=0,p=gn(c,this.__takeCount__);if(!n||!r&&i==c&&p==c)return li(e,this.__actions__);var h=[];e:for(;c--&&d<p;){for(var v=-1,m=e[u+=t];++v<l;){var g=f[v],y=g.iteratee,_=g.type,C=y(m);if(2==_)m=C;else if(!C){if(1==_)continue e;break e}}h[d++]=m}return h},Un.prototype.at=pa,Un.prototype.chain=function(){return la(this)},Un.prototype.commit=function(){return new Bn(this.value(),this.__chain__)},Un.prototype.next=function(){this.__values__===i&&(this.__values__=ls(this.value()));var e=this.__index__>=this.__values__.length;return{done:e,value:e?i:this.__values__[this.__index__++]}},Un.prototype.plant=function(e){for(var t,n=this;n instanceof Gn;){var r=Uo(n);r.__index__=0,r.__values__=i,t?o.__wrapped__=r:t=r;var o=r;n=n.__wrapped__}return o.__wrapped__=e,t},Un.prototype.reverse=function(){var e=this.__wrapped__;if(e instanceof Fn){var t=e;return this.__actions__.length&&(t=new Fn(this)),(t=t.reverse()).__actions__.push({func:da,args:[Qo],thisArg:i}),new Bn(t,this.__chain__)}return this.thru(Qo)},Un.prototype.toJSON=Un.prototype.valueOf=Un.prototype.value=function(){return li(this.__wrapped__,this.__actions__)},Un.prototype.first=Un.prototype.head,Ke&&(Un.prototype[Ke]=function(){return this}),Un}();lt._=hn,(r=function(){return hn}.call(t,n,t,e))===i||(e.exports=r)}.call(this)},963:e=>{"use strict";var t={generateIdentifier:function(){return Math.random().toString(36).substr(2,10)}};t.localCName=t.generateIdentifier(),t.splitLines=function(e){return e.trim().split("\n").map((function(e){return e.trim()}))},t.splitSections=function(e){return e.split("\nm=").map((function(e,t){return(t>0?"m="+e:e).trim()+"\r\n"}))},t.getDescription=function(e){var n=t.splitSections(e);return n&&n[0]},t.getMediaSections=function(e){var n=t.splitSections(e);return n.shift(),n},t.matchPrefix=function(e,n){return t.splitLines(e).filter((function(e){return 0===e.indexOf(n)}))},t.parseCandidate=function(e){for(var t,n={foundation:(t=0===e.indexOf("a=candidate:")?e.substring(12).split(" "):e.substring(10).split(" "))[0],component:parseInt(t[1],10),protocol:t[2].toLowerCase(),priority:parseInt(t[3],10),ip:t[4],address:t[4],port:parseInt(t[5],10),type:t[7]},r=8;r<t.length;r+=2)switch(t[r]){case"raddr":n.relatedAddress=t[r+1];break;case"rport":n.relatedPort=parseInt(t[r+1],10);break;case"tcptype":n.tcpType=t[r+1];break;case"ufrag":n.ufrag=t[r+1],n.usernameFragment=t[r+1];break;default:n[t[r]]=t[r+1]}return n},t.writeCandidate=function(e){var t=[];t.push(e.foundation),t.push(e.component),t.push(e.protocol.toUpperCase()),t.push(e.priority),t.push(e.address||e.ip),t.push(e.port);var n=e.type;return t.push("typ"),t.push(n),"host"!==n&&e.relatedAddress&&e.relatedPort&&(t.push("raddr"),t.push(e.relatedAddress),t.push("rport"),t.push(e.relatedPort)),e.tcpType&&"tcp"===e.protocol.toLowerCase()&&(t.push("tcptype"),t.push(e.tcpType)),(e.usernameFragment||e.ufrag)&&(t.push("ufrag"),t.push(e.usernameFragment||e.ufrag)),"candidate:"+t.join(" ")},t.parseIceOptions=function(e){return e.substr(14).split(" ")},t.parseRtpMap=function(e){var t=e.substr(9).split(" "),n={payloadType:parseInt(t.shift(),10)};return t=t[0].split("/"),n.name=t[0],n.clockRate=parseInt(t[1],10),n.channels=3===t.length?parseInt(t[2],10):1,n.numChannels=n.channels,n},t.writeRtpMap=function(e){var t=e.payloadType;void 0!==e.preferredPayloadType&&(t=e.preferredPayloadType);var n=e.channels||e.numChannels||1;return"a=rtpmap:"+t+" "+e.name+"/"+e.clockRate+(1!==n?"/"+n:"")+"\r\n"},t.parseExtmap=function(e){var t=e.substr(9).split(" ");return{id:parseInt(t[0],10),direction:t[0].indexOf("/")>0?t[0].split("/")[1]:"sendrecv",uri:t[1]}},t.writeExtmap=function(e){return"a=extmap:"+(e.id||e.preferredId)+(e.direction&&"sendrecv"!==e.direction?"/"+e.direction:"")+" "+e.uri+"\r\n"},t.parseFmtp=function(e){for(var t,n={},r=e.substr(e.indexOf(" ")+1).split(";"),i=0;i<r.length;i++)n[(t=r[i].trim().split("="))[0].trim()]=t[1];return n},t.writeFmtp=function(e){var t="",n=e.payloadType;if(void 0!==e.preferredPayloadType&&(n=e.preferredPayloadType),e.parameters&&Object.keys(e.parameters).length){var r=[];Object.keys(e.parameters).forEach((function(t){e.parameters[t]?r.push(t+"="+e.parameters[t]):r.push(t)})),t+="a=fmtp:"+n+" "+r.join(";")+"\r\n"}return t},t.parseRtcpFb=function(e){var t=e.substr(e.indexOf(" ")+1).split(" ");return{type:t.shift(),parameter:t.join(" ")}},t.writeRtcpFb=function(e){var t="",n=e.payloadType;return void 0!==e.preferredPayloadType&&(n=e.preferredPayloadType),e.rtcpFeedback&&e.rtcpFeedback.length&&e.rtcpFeedback.forEach((function(e){t+="a=rtcp-fb:"+n+" "+e.type+(e.parameter&&e.parameter.length?" "+e.parameter:"")+"\r\n"})),t},t.parseSsrcMedia=function(e){var t=e.indexOf(" "),n={ssrc:parseInt(e.substr(7,t-7),10)},r=e.indexOf(":",t);return r>-1?(n.attribute=e.substr(t+1,r-t-1),n.value=e.substr(r+1)):n.attribute=e.substr(t+1),n},t.parseSsrcGroup=function(e){var t=e.substr(13).split(" ");return{semantics:t.shift(),ssrcs:t.map((function(e){return parseInt(e,10)}))}},t.getMid=function(e){var n=t.matchPrefix(e,"a=mid:")[0];if(n)return n.substr(6)},t.parseFingerprint=function(e){var t=e.substr(14).split(" ");return{algorithm:t[0].toLowerCase(),value:t[1]}},t.getDtlsParameters=function(e,n){return{role:"auto",fingerprints:t.matchPrefix(e+n,"a=fingerprint:").map(t.parseFingerprint)}},t.writeDtlsParameters=function(e,t){var n="a=setup:"+t+"\r\n";return e.fingerprints.forEach((function(e){n+="a=fingerprint:"+e.algorithm+" "+e.value+"\r\n"})),n},t.parseCryptoLine=function(e){var t=e.substr(9).split(" ");return{tag:parseInt(t[0],10),cryptoSuite:t[1],keyParams:t[2],sessionParams:t.slice(3)}},t.writeCryptoLine=function(e){return"a=crypto:"+e.tag+" "+e.cryptoSuite+" "+("object"==typeof e.keyParams?t.writeCryptoKeyParams(e.keyParams):e.keyParams)+(e.sessionParams?" "+e.sessionParams.join(" "):"")+"\r\n"},t.parseCryptoKeyParams=function(e){if(0!==e.indexOf("inline:"))return null;var t=e.substr(7).split("|");return{keyMethod:"inline",keySalt:t[0],lifeTime:t[1],mkiValue:t[2]?t[2].split(":")[0]:void 0,mkiLength:t[2]?t[2].split(":")[1]:void 0}},t.writeCryptoKeyParams=function(e){return e.keyMethod+":"+e.keySalt+(e.lifeTime?"|"+e.lifeTime:"")+(e.mkiValue&&e.mkiLength?"|"+e.mkiValue+":"+e.mkiLength:"")},t.getCryptoParameters=function(e,n){return t.matchPrefix(e+n,"a=crypto:").map(t.parseCryptoLine)},t.getIceParameters=function(e,n){var r=t.matchPrefix(e+n,"a=ice-ufrag:")[0],i=t.matchPrefix(e+n,"a=ice-pwd:")[0];return r&&i?{usernameFragment:r.substr(12),password:i.substr(10)}:null},t.writeIceParameters=function(e){return"a=ice-ufrag:"+e.usernameFragment+"\r\na=ice-pwd:"+e.password+"\r\n"},t.parseRtpParameters=function(e){for(var n={codecs:[],headerExtensions:[],fecMechanisms:[],rtcp:[]},r=t.splitLines(e)[0].split(" "),i=3;i<r.length;i++){var o=r[i],a=t.matchPrefix(e,"a=rtpmap:"+o+" ")[0];if(a){var s=t.parseRtpMap(a),c=t.matchPrefix(e,"a=fmtp:"+o+" ");switch(s.parameters=c.length?t.parseFmtp(c[0]):{},s.rtcpFeedback=t.matchPrefix(e,"a=rtcp-fb:"+o+" ").map(t.parseRtcpFb),n.codecs.push(s),s.name.toUpperCase()){case"RED":case"ULPFEC":n.fecMechanisms.push(s.name.toUpperCase())}}}return t.matchPrefix(e,"a=extmap:").forEach((function(e){n.headerExtensions.push(t.parseExtmap(e))})),n},t.writeRtpDescription=function(e,n){var r="";r+="m="+e+" ",r+=n.codecs.length>0?"9":"0",r+=" UDP/TLS/RTP/SAVPF ",r+=n.codecs.map((function(e){return void 0!==e.preferredPayloadType?e.preferredPayloadType:e.payloadType})).join(" ")+"\r\n",r+="c=IN IP4 0.0.0.0\r\n",r+="a=rtcp:9 IN IP4 0.0.0.0\r\n",n.codecs.forEach((function(e){r+=t.writeRtpMap(e),r+=t.writeFmtp(e),r+=t.writeRtcpFb(e)}));var i=0;return n.codecs.forEach((function(e){e.maxptime>i&&(i=e.maxptime)})),i>0&&(r+="a=maxptime:"+i+"\r\n"),r+="a=rtcp-mux\r\n",n.headerExtensions&&n.headerExtensions.forEach((function(e){r+=t.writeExtmap(e)})),r},t.parseRtpEncodingParameters=function(e){var n,r=[],i=t.parseRtpParameters(e),o=-1!==i.fecMechanisms.indexOf("RED"),a=-1!==i.fecMechanisms.indexOf("ULPFEC"),s=t.matchPrefix(e,"a=ssrc:").map((function(e){return t.parseSsrcMedia(e)})).filter((function(e){return"cname"===e.attribute})),c=s.length>0&&s[0].ssrc,u=t.matchPrefix(e,"a=ssrc-group:FID").map((function(e){return e.substr(17).split(" ").map((function(e){return parseInt(e,10)}))}));u.length>0&&u[0].length>1&&u[0][0]===c&&(n=u[0][1]),i.codecs.forEach((function(e){if("RTX"===e.name.toUpperCase()&&e.parameters.apt){var t={ssrc:c,codecPayloadType:parseInt(e.parameters.apt,10)};c&&n&&(t.rtx={ssrc:n}),r.push(t),o&&((t=JSON.parse(JSON.stringify(t))).fec={ssrc:c,mechanism:a?"red+ulpfec":"red"},r.push(t))}})),0===r.length&&c&&r.push({ssrc:c});var f=t.matchPrefix(e,"b=");return f.length&&(f=0===f[0].indexOf("b=TIAS:")?parseInt(f[0].substr(7),10):0===f[0].indexOf("b=AS:")?1e3*parseInt(f[0].substr(5),10)*.95-16e3:void 0,r.forEach((function(e){e.maxBitrate=f}))),r},t.parseRtcpParameters=function(e){var n={},r=t.matchPrefix(e,"a=ssrc:").map((function(e){return t.parseSsrcMedia(e)})).filter((function(e){return"cname"===e.attribute}))[0];r&&(n.cname=r.value,n.ssrc=r.ssrc);var i=t.matchPrefix(e,"a=rtcp-rsize");n.reducedSize=i.length>0,n.compound=0===i.length;var o=t.matchPrefix(e,"a=rtcp-mux");return n.mux=o.length>0,n},t.parseMsid=function(e){var n,r=t.matchPrefix(e,"a=msid:");if(1===r.length)return{stream:(n=r[0].substr(7).split(" "))[0],track:n[1]};var i=t.matchPrefix(e,"a=ssrc:").map((function(e){return t.parseSsrcMedia(e)})).filter((function(e){return"msid"===e.attribute}));return i.length>0?{stream:(n=i[0].value.split(" "))[0],track:n[1]}:void 0},t.parseSctpDescription=function(e){var n,r=t.parseMLine(e),i=t.matchPrefix(e,"a=max-message-size:");i.length>0&&(n=parseInt(i[0].substr(19),10)),isNaN(n)&&(n=65536);var o=t.matchPrefix(e,"a=sctp-port:");if(o.length>0)return{port:parseInt(o[0].substr(12),10),protocol:r.fmt,maxMessageSize:n};if(t.matchPrefix(e,"a=sctpmap:").length>0){var a=t.matchPrefix(e,"a=sctpmap:")[0].substr(10).split(" ");return{port:parseInt(a[0],10),protocol:a[1],maxMessageSize:n}}},t.writeSctpDescription=function(e,t){var n=[];return n="DTLS/SCTP"!==e.protocol?["m="+e.kind+" 9 "+e.protocol+" "+t.protocol+"\r\n","c=IN IP4 0.0.0.0\r\n","a=sctp-port:"+t.port+"\r\n"]:["m="+e.kind+" 9 "+e.protocol+" "+t.port+"\r\n","c=IN IP4 0.0.0.0\r\n","a=sctpmap:"+t.port+" "+t.protocol+" 65535\r\n"],void 0!==t.maxMessageSize&&n.push("a=max-message-size:"+t.maxMessageSize+"\r\n"),n.join("")},t.generateSessionId=function(){return Math.random().toString().substr(2,21)},t.writeSessionBoilerplate=function(e,n,r){var i=void 0!==n?n:2;return"v=0\r\no="+(r||"thisisadapterortc")+" "+(e||t.generateSessionId())+" "+i+" IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\n"},t.writeMediaSection=function(e,n,r,i){var o=t.writeRtpDescription(e.kind,n);if(o+=t.writeIceParameters(e.iceGatherer.getLocalParameters()),o+=t.writeDtlsParameters(e.dtlsTransport.getLocalParameters(),"offer"===r?"actpass":"active"),o+="a=mid:"+e.mid+"\r\n",e.direction?o+="a="+e.direction+"\r\n":e.rtpSender&&e.rtpReceiver?o+="a=sendrecv\r\n":e.rtpSender?o+="a=sendonly\r\n":e.rtpReceiver?o+="a=recvonly\r\n":o+="a=inactive\r\n",e.rtpSender){var a="msid:"+i.id+" "+e.rtpSender.track.id+"\r\n";o+="a="+a,o+="a=ssrc:"+e.sendEncodingParameters[0].ssrc+" "+a,e.sendEncodingParameters[0].rtx&&(o+="a=ssrc:"+e.sendEncodingParameters[0].rtx.ssrc+" "+a,o+="a=ssrc-group:FID "+e.sendEncodingParameters[0].ssrc+" "+e.sendEncodingParameters[0].rtx.ssrc+"\r\n")}return o+="a=ssrc:"+e.sendEncodingParameters[0].ssrc+" cname:"+t.localCName+"\r\n",e.rtpSender&&e.sendEncodingParameters[0].rtx&&(o+="a=ssrc:"+e.sendEncodingParameters[0].rtx.ssrc+" cname:"+t.localCName+"\r\n"),o},t.getDirection=function(e,n){for(var r=t.splitLines(e),i=0;i<r.length;i++)switch(r[i]){case"a=sendrecv":case"a=sendonly":case"a=recvonly":case"a=inactive":return r[i].substr(2)}return n?t.getDirection(n):"sendrecv"},t.getKind=function(e){return t.splitLines(e)[0].split(" ")[0].substr(2)},t.isRejected=function(e){return"0"===e.split(" ",2)[1]},t.parseMLine=function(e){var n=t.splitLines(e)[0].substr(2).split(" ");return{kind:n[0],port:parseInt(n[1],10),protocol:n[2],fmt:n.slice(3).join(" ")}},t.parseOLine=function(e){var n=t.matchPrefix(e,"o=")[0].substr(2).split(" ");return{username:n[0],sessionId:n[1],sessionVersion:parseInt(n[2],10),netType:n[3],addressType:n[4],address:n[5]}},t.isValidSDP=function(e){if("string"!=typeof e||0===e.length)return!1;for(var n=t.splitLines(e),r=0;r<n.length;r++)if(n[r].length<2||"="!==n[r].charAt(1))return!1;return!0},e.exports=t},752:(e,t,n)=>{"use strict";var r=n(525);e.exports=r({window:n.g.window})},525:(e,t,n)=>{"use strict";e.exports=function(e,t){var r=e&&e.window,i={shimChrome:!0,shimFirefox:!0,shimEdge:!0,shimSafari:!0};for(var o in t)hasOwnProperty.call(t,o)&&(i[o]=t[o]);var a=n(634),s=a.log,c=a.detectBrowser(r),u={browserDetails:c,extractVersion:a.extractVersion,disableLog:a.disableLog,disableWarnings:a.disableWarnings},f=n(528)||null,l=n(190)||null,d=n(542)||null,p=n(824)||null;switch(c.browser){case"chrome":if(!f||!f.shimPeerConnection||!i.shimChrome)return s("Chrome shim is not included in this adapter release."),u;s("adapter.js shimming chrome."),u.browserShim=f,f.shimGetUserMedia(r),f.shimMediaStream(r),a.shimCreateObjectURL(r),f.shimSourceObject(r),f.shimPeerConnection(r),f.shimOnTrack(r),f.shimAddTrackRemoveTrack(r),f.shimGetSendersWithDtmf(r);break;case"firefox":if(!d||!d.shimPeerConnection||!i.shimFirefox)return s("Firefox shim is not included in this adapter release."),u;s("adapter.js shimming firefox."),u.browserShim=d,d.shimGetUserMedia(r),a.shimCreateObjectURL(r),d.shimSourceObject(r),d.shimPeerConnection(r),d.shimOnTrack(r);break;case"edge":if(!l||!l.shimPeerConnection||!i.shimEdge)return s("MS edge shim is not included in this adapter release."),u;s("adapter.js shimming edge."),u.browserShim=l,l.shimGetUserMedia(r),a.shimCreateObjectURL(r),l.shimPeerConnection(r),l.shimReplaceTrack(r);break;case"safari":if(!p||!i.shimSafari)return s("Safari shim is not included in this adapter release."),u;s("adapter.js shimming safari."),u.browserShim=p,a.shimCreateObjectURL(r),p.shimRTCIceServerUrls(r),p.shimCallbacksAPI(r),p.shimLocalStreamsAPI(r),p.shimRemoteStreamsAPI(r),p.shimGetUserMedia(r);break;default:s("Unsupported browser!")}return u}},528:(e,t,n)=>{"use strict";var r=n(634),i=r.log,o={shimMediaStream:function(e){e.MediaStream=e.MediaStream||e.webkitMediaStream},shimOnTrack:function(e){if("object"==typeof e&&e.RTCPeerConnection&&!("ontrack"in e.RTCPeerConnection.prototype)){Object.defineProperty(e.RTCPeerConnection.prototype,"ontrack",{get:function(){return this._ontrack},set:function(e){this._ontrack&&this.removeEventListener("track",this._ontrack),this.addEventListener("track",this._ontrack=e)}});var t=e.RTCPeerConnection.prototype.setRemoteDescription;e.RTCPeerConnection.prototype.setRemoteDescription=function(){var n=this;return n._ontrackpoly||(n._ontrackpoly=function(t){t.stream.addEventListener("addtrack",(function(r){var i;i=e.RTCPeerConnection.prototype.getReceivers?n.getReceivers().find((function(e){return e.track&&e.track.id===r.track.id})):{track:r.track};var o=new Event("track");o.track=r.track,o.receiver=i,o.streams=[t.stream],n.dispatchEvent(o)})),t.stream.getTracks().forEach((function(r){var i;i=e.RTCPeerConnection.prototype.getReceivers?n.getReceivers().find((function(e){return e.track&&e.track.id===r.id})):{track:r};var o=new Event("track");o.track=r,o.receiver=i,o.streams=[t.stream],n.dispatchEvent(o)}))},n.addEventListener("addstream",n._ontrackpoly)),t.apply(n,arguments)}}},shimGetSendersWithDtmf:function(e){if("object"==typeof e&&e.RTCPeerConnection&&!("getSenders"in e.RTCPeerConnection.prototype)&&"createDTMFSender"in e.RTCPeerConnection.prototype){var t=function(e,t){return{track:t,get dtmf(){return void 0===this._dtmf&&("audio"===t.kind?this._dtmf=e.createDTMFSender(t):this._dtmf=null),this._dtmf},_pc:e}};if(!e.RTCPeerConnection.prototype.getSenders){e.RTCPeerConnection.prototype.getSenders=function(){return this._senders=this._senders||[],this._senders.slice()};var n=e.RTCPeerConnection.prototype.addTrack;e.RTCPeerConnection.prototype.addTrack=function(e,r){var i=this,o=n.apply(i,arguments);return o||(o=t(i,e),i._senders.push(o)),o};var r=e.RTCPeerConnection.prototype.removeTrack;e.RTCPeerConnection.prototype.removeTrack=function(e){var t=this;r.apply(t,arguments);var n=t._senders.indexOf(e);-1!==n&&t._senders.splice(n,1)}}var i=e.RTCPeerConnection.prototype.addStream;e.RTCPeerConnection.prototype.addStream=function(e){var n=this;n._senders=n._senders||[],i.apply(n,[e]),e.getTracks().forEach((function(e){n._senders.push(t(n,e))}))};var o=e.RTCPeerConnection.prototype.removeStream;e.RTCPeerConnection.prototype.removeStream=function(e){var t=this;t._senders=t._senders||[],o.apply(t,[t._streams[e.id]||e]),e.getTracks().forEach((function(e){var n=t._senders.find((function(t){return t.track===e}));n&&t._senders.splice(t._senders.indexOf(n),1)}))}}else if("object"==typeof e&&e.RTCPeerConnection&&"getSenders"in e.RTCPeerConnection.prototype&&"createDTMFSender"in e.RTCPeerConnection.prototype&&e.RTCRtpSender&&!("dtmf"in e.RTCRtpSender.prototype)){var a=e.RTCPeerConnection.prototype.getSenders;e.RTCPeerConnection.prototype.getSenders=function(){var e=this,t=a.apply(e,[]);return t.forEach((function(t){t._pc=e})),t},Object.defineProperty(e.RTCRtpSender.prototype,"dtmf",{get:function(){return void 0===this._dtmf&&("audio"===this.track.kind?this._dtmf=this._pc.createDTMFSender(this.track):this._dtmf=null),this._dtmf}})}},shimSourceObject:function(e){var t=e&&e.URL;"object"==typeof e&&e.HTMLMediaElement&&!("srcObject"in e.HTMLMediaElement.prototype)&&Object.defineProperty(e.HTMLMediaElement.prototype,"srcObject",{get:function(){return this._srcObject},set:function(e){var n=this;this._srcObject=e,this.src&&t.revokeObjectURL(this.src),e?(this.src=t.createObjectURL(e),e.addEventListener("addtrack",(function(){n.src&&t.revokeObjectURL(n.src),n.src=t.createObjectURL(e)})),e.addEventListener("removetrack",(function(){n.src&&t.revokeObjectURL(n.src),n.src=t.createObjectURL(e)}))):this.src=""}})},shimAddTrackRemoveTrack:function(e){if(!e.RTCPeerConnection.prototype.addTrack){var t=e.RTCPeerConnection.prototype.getLocalStreams;e.RTCPeerConnection.prototype.getLocalStreams=function(){var e=this,n=t.apply(this);return e._reverseStreams=e._reverseStreams||{},n.map((function(t){return e._reverseStreams[t.id]}))};var n=e.RTCPeerConnection.prototype.addStream;e.RTCPeerConnection.prototype.addStream=function(t){var r=this;if(r._streams=r._streams||{},r._reverseStreams=r._reverseStreams||{},t.getTracks().forEach((function(e){if(r.getSenders().find((function(t){return t.track===e})))throw new DOMException("Track already exists.","InvalidAccessError")})),!r._reverseStreams[t.id]){var i=new e.MediaStream(t.getTracks());r._streams[t.id]=i,r._reverseStreams[i.id]=t,t=i}n.apply(r,[t])};var r=e.RTCPeerConnection.prototype.removeStream;e.RTCPeerConnection.prototype.removeStream=function(e){var t=this;t._streams=t._streams||{},t._reverseStreams=t._reverseStreams||{},r.apply(t,[t._streams[e.id]||e]),delete t._reverseStreams[t._streams[e.id]?t._streams[e.id].id:e.id],delete t._streams[e.id]},e.RTCPeerConnection.prototype.addTrack=function(t,n){var r=this;if("closed"===r.signalingState)throw new DOMException("The RTCPeerConnection's signalingState is 'closed'.","InvalidStateError");var i=[].slice.call(arguments,1);if(1!==i.length||!i[0].getTracks().find((function(e){return e===t})))throw new DOMException("The adapter.js addTrack polyfill only supports a single  stream which is associated with the specified track.","NotSupportedError");if(r.getSenders().find((function(e){return e.track===t})))throw new DOMException("Track already exists.","InvalidAccessError");r._streams=r._streams||{},r._reverseStreams=r._reverseStreams||{};var o=r._streams[n.id];if(o)o.addTrack(t),r.dispatchEvent(new Event("negotiationneeded"));else{var a=new e.MediaStream([t]);r._streams[n.id]=a,r._reverseStreams[a.id]=n,r.addStream(a)}return r.getSenders().find((function(e){return e.track===t}))},e.RTCPeerConnection.prototype.removeTrack=function(e){var t,n=this;if("closed"===n.signalingState)throw new DOMException("The RTCPeerConnection's signalingState is 'closed'.","InvalidStateError");if(!e._pc)throw new DOMException("Argument 1 of RTCPeerConnection.removeTrack does not implement interface RTCRtpSender.","TypeError");if(e._pc!==n)throw new DOMException("Sender was not created by this connection.","InvalidAccessError");n._streams=n._streams||{},Object.keys(n._streams).forEach((function(r){n._streams[r].getTracks().find((function(t){return e.track===t}))&&(t=n._streams[r])})),t&&(1===t.getTracks().length?n.removeStream(t):t.removeTrack(e.track),n.dispatchEvent(new Event("negotiationneeded")))}}},shimPeerConnection:function(e){var t=r.detectBrowser(e);if(e.RTCPeerConnection){var n=e.RTCPeerConnection;e.RTCPeerConnection=function(e,t){if(e&&e.iceServers){for(var i=[],o=0;o<e.iceServers.length;o++){var a=e.iceServers[o];!a.hasOwnProperty("urls")&&a.hasOwnProperty("url")?(r.deprecated("RTCIceServer.url","RTCIceServer.urls"),(a=JSON.parse(JSON.stringify(a))).urls=a.url,i.push(a)):i.push(e.iceServers[o])}e.iceServers=i}return new n(e,t)},e.RTCPeerConnection.prototype=n.prototype,Object.defineProperty(e.RTCPeerConnection,"generateCertificate",{get:function(){return n.generateCertificate}})}else e.RTCPeerConnection=function(t,n){return i("PeerConnection"),t&&t.iceTransportPolicy&&(t.iceTransports=t.iceTransportPolicy),new e.webkitRTCPeerConnection(t,n)},e.RTCPeerConnection.prototype=e.webkitRTCPeerConnection.prototype,e.webkitRTCPeerConnection.generateCertificate&&Object.defineProperty(e.RTCPeerConnection,"generateCertificate",{get:function(){return e.webkitRTCPeerConnection.generateCertificate}});var o=e.RTCPeerConnection.prototype.getStats;e.RTCPeerConnection.prototype.getStats=function(e,t,n){var r=this,i=arguments;if(arguments.length>0&&"function"==typeof e)return o.apply(this,arguments);if(0===o.length&&(0===arguments.length||"function"!=typeof arguments[0]))return o.apply(this,[]);var a=function(e){var t={};return e.result().forEach((function(e){var n={id:e.id,timestamp:e.timestamp,type:{localcandidate:"local-candidate",remotecandidate:"remote-candidate"}[e.type]||e.type};e.names().forEach((function(t){n[t]=e.stat(t)})),t[n.id]=n})),t},s=function(e){return new Map(Object.keys(e).map((function(t){return[t,e[t]]})))};return arguments.length>=2?o.apply(this,[function(e){i[1](s(a(e)))},arguments[0]]):new Promise((function(e,t){o.apply(r,[function(t){e(s(a(t)))},t])})).then(t,n)},t.version<51&&["setLocalDescription","setRemoteDescription","addIceCandidate"].forEach((function(t){var n=e.RTCPeerConnection.prototype[t];e.RTCPeerConnection.prototype[t]=function(){var e=arguments,t=this,r=new Promise((function(r,i){n.apply(t,[e[0],r,i])}));return e.length<2?r:r.then((function(){e[1].apply(null,[])}),(function(t){e.length>=3&&e[2].apply(null,[t])}))}})),t.version<52&&["createOffer","createAnswer"].forEach((function(t){var n=e.RTCPeerConnection.prototype[t];e.RTCPeerConnection.prototype[t]=function(){var e=this;if(arguments.length<1||1===arguments.length&&"object"==typeof arguments[0]){var t=1===arguments.length?arguments[0]:void 0;return new Promise((function(r,i){n.apply(e,[r,i,t])}))}return n.apply(this,arguments)}})),["setLocalDescription","setRemoteDescription","addIceCandidate"].forEach((function(t){var n=e.RTCPeerConnection.prototype[t];e.RTCPeerConnection.prototype[t]=function(){return arguments[0]=new("addIceCandidate"===t?e.RTCIceCandidate:e.RTCSessionDescription)(arguments[0]),n.apply(this,arguments)}}));var a=e.RTCPeerConnection.prototype.addIceCandidate;e.RTCPeerConnection.prototype.addIceCandidate=function(){return arguments[0]?a.apply(this,arguments):(arguments[1]&&arguments[1].apply(null),Promise.resolve())}}};e.exports={shimMediaStream:o.shimMediaStream,shimOnTrack:o.shimOnTrack,shimAddTrackRemoveTrack:o.shimAddTrackRemoveTrack,shimGetSendersWithDtmf:o.shimGetSendersWithDtmf,shimSourceObject:o.shimSourceObject,shimPeerConnection:o.shimPeerConnection,shimGetUserMedia:n(293)}},293:(e,t,n)=>{"use strict";var r=n(634),i=r.log;e.exports=function(e){var t=r.detectBrowser(e),n=e&&e.navigator,o=function(e){if("object"!=typeof e||e.mandatory||e.optional)return e;var t={};return Object.keys(e).forEach((function(n){if("require"!==n&&"advanced"!==n&&"mediaSource"!==n){var r="object"==typeof e[n]?e[n]:{ideal:e[n]};void 0!==r.exact&&"number"==typeof r.exact&&(r.min=r.max=r.exact);var i=function(e,t){return e?e+t.charAt(0).toUpperCase()+t.slice(1):"deviceId"===t?"sourceId":t};if(void 0!==r.ideal){t.optional=t.optional||[];var o={};"number"==typeof r.ideal?(o[i("min",n)]=r.ideal,t.optional.push(o),(o={})[i("max",n)]=r.ideal,t.optional.push(o)):(o[i("",n)]=r.ideal,t.optional.push(o))}void 0!==r.exact&&"number"!=typeof r.exact?(t.mandatory=t.mandatory||{},t.mandatory[i("",n)]=r.exact):["min","max"].forEach((function(e){void 0!==r[e]&&(t.mandatory=t.mandatory||{},t.mandatory[i(e,n)]=r[e])}))}})),e.advanced&&(t.optional=(t.optional||[]).concat(e.advanced)),t},a=function(e,r){if((e=JSON.parse(JSON.stringify(e)))&&"object"==typeof e.audio){var a=function(e,t,n){t in e&&!(n in e)&&(e[n]=e[t],delete e[t])};a((e=JSON.parse(JSON.stringify(e))).audio,"autoGainControl","googAutoGainControl"),a(e.audio,"noiseSuppression","googNoiseSuppression"),e.audio=o(e.audio)}if(e&&"object"==typeof e.video){var s=e.video.facingMode;s=s&&("object"==typeof s?s:{ideal:s});var c,u=t.version<61;if(s&&("user"===s.exact||"environment"===s.exact||"user"===s.ideal||"environment"===s.ideal)&&(!n.mediaDevices.getSupportedConstraints||!n.mediaDevices.getSupportedConstraints().facingMode||u)&&(delete e.video.facingMode,"environment"===s.exact||"environment"===s.ideal?c=["back","rear"]:"user"!==s.exact&&"user"!==s.ideal||(c=["front"]),c))return n.mediaDevices.enumerateDevices().then((function(t){var n=(t=t.filter((function(e){return"videoinput"===e.kind}))).find((function(e){return c.some((function(t){return-1!==e.label.toLowerCase().indexOf(t)}))}));return!n&&t.length&&-1!==c.indexOf("back")&&(n=t[t.length-1]),n&&(e.video.deviceId=s.exact?{exact:n.deviceId}:{ideal:n.deviceId}),e.video=o(e.video),i("chrome: "+JSON.stringify(e)),r(e)}));e.video=o(e.video)}return i("chrome: "+JSON.stringify(e)),r(e)},s=function(e){return{name:{PermissionDeniedError:"NotAllowedError",InvalidStateError:"NotReadableError",DevicesNotFoundError:"NotFoundError",ConstraintNotSatisfiedError:"OverconstrainedError",TrackStartError:"NotReadableError",MediaDeviceFailedDueToShutdown:"NotReadableError",MediaDeviceKillSwitchOn:"NotReadableError"}[e.name]||e.name,message:e.message,constraint:e.constraintName,toString:function(){return this.name+(this.message&&": ")+this.message}}};n.getUserMedia=function(e,t,r){a(e,(function(e){n.webkitGetUserMedia(e,t,(function(e){r(s(e))}))}))};var c=function(e){return new Promise((function(t,r){n.getUserMedia(e,t,r)}))};if(n.mediaDevices||(n.mediaDevices={getUserMedia:c,enumerateDevices:function(){return new Promise((function(t){var n={audio:"audioinput",video:"videoinput"};return e.MediaStreamTrack.getSources((function(e){t(e.map((function(e){return{label:e.label,kind:n[e.kind],deviceId:e.id,groupId:""}})))}))}))},getSupportedConstraints:function(){return{deviceId:!0,echoCancellation:!0,facingMode:!0,frameRate:!0,height:!0,width:!0}}}),n.mediaDevices.getUserMedia){var u=n.mediaDevices.getUserMedia.bind(n.mediaDevices);n.mediaDevices.getUserMedia=function(e){return a(e,(function(e){return u(e).then((function(t){if(e.audio&&!t.getAudioTracks().length||e.video&&!t.getVideoTracks().length)throw t.getTracks().forEach((function(e){e.stop()})),new DOMException("","NotFoundError");return t}),(function(e){return Promise.reject(s(e))}))}))}}else n.mediaDevices.getUserMedia=function(e){return c(e)};void 0===n.mediaDevices.addEventListener&&(n.mediaDevices.addEventListener=function(){i("Dummy mediaDevices.addEventListener called.")}),void 0===n.mediaDevices.removeEventListener&&(n.mediaDevices.removeEventListener=function(){i("Dummy mediaDevices.removeEventListener called.")})}},190:(e,t,n)=>{"use strict";var r=n(634),i=n(928);e.exports={shimGetUserMedia:n(684),shimPeerConnection:function(e){var t=r.detectBrowser(e);if(e.RTCIceGatherer&&(e.RTCIceCandidate||(e.RTCIceCandidate=function(e){return e}),e.RTCSessionDescription||(e.RTCSessionDescription=function(e){return e}),t.version<15025)){var n=Object.getOwnPropertyDescriptor(e.MediaStreamTrack.prototype,"enabled");Object.defineProperty(e.MediaStreamTrack.prototype,"enabled",{set:function(e){n.set.call(this,e);var t=new Event("enabled");t.enabled=e,this.dispatchEvent(t)}})}e.RTCRtpSender&&!("dtmf"in e.RTCRtpSender.prototype)&&Object.defineProperty(e.RTCRtpSender.prototype,"dtmf",{get:function(){return void 0===this._dtmf&&("audio"===this.track.kind?this._dtmf=new e.RTCDtmfSender(this):"video"===this.track.kind&&(this._dtmf=null)),this._dtmf}}),e.RTCPeerConnection=i(e,t.version)},shimReplaceTrack:function(e){e.RTCRtpSender&&!("replaceTrack"in e.RTCRtpSender.prototype)&&(e.RTCRtpSender.prototype.replaceTrack=e.RTCRtpSender.prototype.setTrack)}}},684:e=>{"use strict";e.exports=function(e){var t=e&&e.navigator,n=t.mediaDevices.getUserMedia.bind(t.mediaDevices);t.mediaDevices.getUserMedia=function(e){return n(e).catch((function(e){return Promise.reject(function(e){return{name:{PermissionDeniedError:"NotAllowedError"}[e.name]||e.name,message:e.message,constraint:e.constraint,toString:function(){return this.name}}}(e))}))}}},928:(e,t,n)=>{"use strict";var r=n(963);function i(e,t){var n={codecs:[],headerExtensions:[],fecMechanisms:[]},r=function(e,t){e=parseInt(e,10);for(var n=0;n<t.length;n++)if(t[n].payloadType===e||t[n].preferredPayloadType===e)return t[n]},i=function(e,t,n,i){var o=r(e.parameters.apt,n),a=r(t.parameters.apt,i);return o&&a&&o.name.toLowerCase()===a.name.toLowerCase()};return e.codecs.forEach((function(r){for(var o=0;o<t.codecs.length;o++){var a=t.codecs[o];if(r.name.toLowerCase()===a.name.toLowerCase()&&r.clockRate===a.clockRate){if("rtx"===r.name.toLowerCase()&&r.parameters&&a.parameters.apt&&!i(r,a,e.codecs,t.codecs))continue;(a=JSON.parse(JSON.stringify(a))).numChannels=Math.min(r.numChannels,a.numChannels),n.codecs.push(a),a.rtcpFeedback=a.rtcpFeedback.filter((function(e){for(var t=0;t<r.rtcpFeedback.length;t++)if(r.rtcpFeedback[t].type===e.type&&r.rtcpFeedback[t].parameter===e.parameter)return!0;return!1}));break}}})),e.headerExtensions.forEach((function(e){for(var r=0;r<t.headerExtensions.length;r++){var i=t.headerExtensions[r];if(e.uri===i.uri){n.headerExtensions.push(i);break}}})),n}function o(e,t,n){return-1!=={offer:{setLocalDescription:["stable","have-local-offer"],setRemoteDescription:["stable","have-remote-offer"]},answer:{setLocalDescription:["have-remote-offer","have-local-pranswer"],setRemoteDescription:["have-local-offer","have-remote-pranswer"]}}[t][e].indexOf(n)}e.exports=function(e,t){var n=function(n){var i=this,o=document.createDocumentFragment();if(["addEventListener","removeEventListener","dispatchEvent"].forEach((function(e){i[e]=o[e].bind(o)})),this.needNegotiation=!1,this.onicecandidate=null,this.onaddstream=null,this.ontrack=null,this.onremovestream=null,this.onsignalingstatechange=null,this.oniceconnectionstatechange=null,this.onicegatheringstatechange=null,this.onnegotiationneeded=null,this.ondatachannel=null,this.canTrickleIceCandidates=null,this.localStreams=[],this.remoteStreams=[],this.getLocalStreams=function(){return i.localStreams},this.getRemoteStreams=function(){return i.remoteStreams},this.localDescription=new e.RTCSessionDescription({type:"",sdp:""}),this.remoteDescription=new e.RTCSessionDescription({type:"",sdp:""}),this.signalingState="stable",this.iceConnectionState="new",this.iceGatheringState="new",this.iceOptions={gatherPolicy:"all",iceServers:[]},n&&n.iceTransportPolicy)switch(n.iceTransportPolicy){case"all":case"relay":this.iceOptions.gatherPolicy=n.iceTransportPolicy}this.usingBundle=n&&"max-bundle"===n.bundlePolicy,n&&n.iceServers&&(this.iceOptions.iceServers=function(e,t){var n=!1;return(e=JSON.parse(JSON.stringify(e))).filter((function(e){if(e&&(e.urls||e.url)){var r=e.urls||e.url;e.url&&!e.urls&&console.warn("RTCIceServer.url is deprecated! Use urls instead.");var i="string"==typeof r;return i&&(r=[r]),r=r.filter((function(e){return 0!==e.indexOf("turn:")||-1===e.indexOf("transport=udp")||-1!==e.indexOf("turn:[")||n?0===e.indexOf("stun:")&&t>=14393:(n=!0,!0)})),delete e.url,e.urls=i?r[0]:r,!!r.length}return!1}))}(n.iceServers,t)),this._config=n||{},this.transceivers=[],this._localIceCandidatesBuffer=[],this._sdpSessionId=r.generateSessionId()};return n.prototype._emitGatheringStateChange=function(){var e=new Event("icegatheringstatechange");this.dispatchEvent(e),null!==this.onicegatheringstatechange&&this.onicegatheringstatechange(e)},n.prototype._emitBufferedCandidates=function(){var e=this,t=r.splitSections(e.localDescription.sdp);this._localIceCandidatesBuffer.forEach((function(n){if(n.candidate&&0!==Object.keys(n.candidate).length)t[n.candidate.sdpMLineIndex+1]+="a="+n.candidate.candidate+"\r\n";else for(var r=1;r<t.length;r++)-1===t[r].indexOf("\r\na=end-of-candidates\r\n")&&(t[r]+="a=end-of-candidates\r\n");e.localDescription.sdp=t.join(""),e.dispatchEvent(n),null!==e.onicecandidate&&e.onicecandidate(n),n.candidate||"complete"===e.iceGatheringState||e.transceivers.every((function(e){return e.iceGatherer&&"completed"===e.iceGatherer.state}))&&"complete"!==e.iceGatheringStateChange&&(e.iceGatheringState="complete",e._emitGatheringStateChange())})),this._localIceCandidatesBuffer=[]},n.prototype.getConfiguration=function(){return this._config},n.prototype._createTransceiver=function(e){var t=this.transceivers.length>0,n={track:null,iceGatherer:null,iceTransport:null,dtlsTransport:null,localCapabilities:null,remoteCapabilities:null,rtpSender:null,rtpReceiver:null,kind:e,mid:null,sendEncodingParameters:null,recvEncodingParameters:null,stream:null,wantReceive:!0};if(this.usingBundle&&t)n.iceTransport=this.transceivers[0].iceTransport,n.dtlsTransport=this.transceivers[0].dtlsTransport;else{var r=this._createIceAndDtlsTransports();n.iceTransport=r.iceTransport,n.dtlsTransport=r.dtlsTransport}return this.transceivers.push(n),n},n.prototype.addTrack=function(t,n){for(var r,i=0;i<this.transceivers.length;i++)this.transceivers[i].track||this.transceivers[i].kind!==t.kind||(r=this.transceivers[i]);return r||(r=this._createTransceiver(t.kind)),r.track=t,r.stream=n,r.rtpSender=new e.RTCRtpSender(t,r.dtlsTransport),this._maybeFireNegotiationNeeded(),r.rtpSender},n.prototype.addStream=function(e){var n=this;if(t>=15025)this.localStreams.push(e),e.getTracks().forEach((function(t){n.addTrack(t,e)}));else{var r=e.clone();e.getTracks().forEach((function(e,t){var n=r.getTracks()[t];e.addEventListener("enabled",(function(e){n.enabled=e.enabled}))})),r.getTracks().forEach((function(e){n.addTrack(e,r)})),this.localStreams.push(r)}this._maybeFireNegotiationNeeded()},n.prototype.removeStream=function(e){var t=this.localStreams.indexOf(e);t>-1&&(this.localStreams.splice(t,1),this._maybeFireNegotiationNeeded())},n.prototype.getSenders=function(){return this.transceivers.filter((function(e){return!!e.rtpSender})).map((function(e){return e.rtpSender}))},n.prototype.getReceivers=function(){return this.transceivers.filter((function(e){return!!e.rtpReceiver})).map((function(e){return e.rtpReceiver}))},n.prototype._createIceGatherer=function(t,n){var i=this,o=new e.RTCIceGatherer(i.iceOptions);return o.onlocalcandidate=function(e){var a=new Event("icecandidate");a.candidate={sdpMid:t,sdpMLineIndex:n};var s=e.candidate,c=!s||0===Object.keys(s).length;c?void 0===o.state&&(o.state="completed"):(s.component=1,a.candidate.candidate=r.writeCandidate(s));var u=r.splitSections(i.localDescription.sdp);u[a.candidate.sdpMLineIndex+1]+=c?"a=end-of-candidates\r\n":"a="+a.candidate.candidate+"\r\n",i.localDescription.sdp=u.join("");var f=(i._pendingOffer?i._pendingOffer:i.transceivers).every((function(e){return e.iceGatherer&&"completed"===e.iceGatherer.state}));switch(i.iceGatheringState){case"new":c||i._localIceCandidatesBuffer.push(a),c&&f&&i._localIceCandidatesBuffer.push(new Event("icecandidate"));break;case"gathering":i._emitBufferedCandidates(),c||(i.dispatchEvent(a),null!==i.onicecandidate&&i.onicecandidate(a)),f&&(i.dispatchEvent(new Event("icecandidate")),null!==i.onicecandidate&&i.onicecandidate(new Event("icecandidate")),i.iceGatheringState="complete",i._emitGatheringStateChange())}},o},n.prototype._createIceAndDtlsTransports=function(){var t=this,n=new e.RTCIceTransport(null);n.onicestatechange=function(){t._updateConnectionState()};var r=new e.RTCDtlsTransport(n);return r.ondtlsstatechange=function(){t._updateConnectionState()},r.onerror=function(){Object.defineProperty(r,"state",{value:"failed",writable:!0}),t._updateConnectionState()},{iceTransport:n,dtlsTransport:r}},n.prototype._disposeIceAndDtlsTransports=function(e){var t=this.transceivers[e].iceGatherer;t&&(delete t.onlocalcandidate,delete this.transceivers[e].iceGatherer);var n=this.transceivers[e].iceTransport;n&&(delete n.onicestatechange,delete this.transceivers[e].iceTransport);var r=this.transceivers[e].dtlsTransport;r&&(delete r.ondtlsstatechange,delete r.onerror,delete this.transceivers[e].dtlsTransport)},n.prototype._transceive=function(e,n,o){var a=i(e.localCapabilities,e.remoteCapabilities);n&&e.rtpSender&&(a.encodings=e.sendEncodingParameters,a.rtcp={cname:r.localCName,compound:e.rtcpParameters.compound},e.recvEncodingParameters.length&&(a.rtcp.ssrc=e.recvEncodingParameters[0].ssrc),e.rtpSender.send(a)),o&&e.rtpReceiver&&("video"===e.kind&&e.recvEncodingParameters&&t<15019&&e.recvEncodingParameters.forEach((function(e){delete e.rtx})),a.encodings=e.recvEncodingParameters,a.rtcp={cname:e.rtcpParameters.cname,compound:e.rtcpParameters.compound},e.sendEncodingParameters.length&&(a.rtcp.ssrc=e.sendEncodingParameters[0].ssrc),e.rtpReceiver.receive(a))},n.prototype.setLocalDescription=function(t){var n,a,s=this;if(!o("setLocalDescription",t.type,this.signalingState)){var c=new Error("Can not set local "+t.type+" in state "+this.signalingState);return c.name="InvalidStateError",arguments.length>2&&"function"==typeof arguments[2]&&e.setTimeout(arguments[2],0,c),Promise.reject(c)}if("offer"===t.type)this._pendingOffer&&(n=r.splitSections(t.sdp),a=n.shift(),n.forEach((function(e,t){var n=r.parseRtpParameters(e);s._pendingOffer[t].localCapabilities=n})),this.transceivers=this._pendingOffer,delete this._pendingOffer);else if("answer"===t.type){n=r.splitSections(s.remoteDescription.sdp),a=n.shift();var u=r.matchPrefix(a,"a=ice-lite").length>0;n.forEach((function(e,t){var n=s.transceivers[t],o=n.iceGatherer,c=n.iceTransport,f=n.dtlsTransport,l=n.localCapabilities,d=n.remoteCapabilities;if(!r.isRejected(e)&&!n.isDatachannel){var p=r.getIceParameters(e,a),h=r.getDtlsParameters(e,a);u&&(h.role="server"),s.usingBundle&&0!==t||(c.start(o,p,u?"controlling":"controlled"),f.start(h));var v=i(l,d);s._transceive(n,v.codecs.length>0,!1)}}))}switch(this.localDescription={type:t.type,sdp:t.sdp},t.type){case"offer":this._updateSignalingState("have-local-offer");break;case"answer":this._updateSignalingState("stable");break;default:throw new TypeError('unsupported type "'+t.type+'"')}var f=arguments.length>1&&"function"==typeof arguments[1];if(f){var l=arguments[1];e.setTimeout((function(){l(),"new"===s.iceGatheringState&&(s.iceGatheringState="gathering",s._emitGatheringStateChange()),s._emitBufferedCandidates()}),0)}var d=Promise.resolve();return d.then((function(){f||("new"===s.iceGatheringState&&(s.iceGatheringState="gathering",s._emitGatheringStateChange()),e.setTimeout(s._emitBufferedCandidates.bind(s),500))})),d},n.prototype.setRemoteDescription=function(n){var i=this;if(!o("setRemoteDescription",n.type,this.signalingState)){var a=new Error("Can not set remote "+n.type+" in state "+this.signalingState);return a.name="InvalidStateError",arguments.length>2&&"function"==typeof arguments[2]&&e.setTimeout(arguments[2],0,a),Promise.reject(a)}var s={},c=[],u=r.splitSections(n.sdp),f=u.shift(),l=r.matchPrefix(f,"a=ice-lite").length>0,d=r.matchPrefix(f,"a=group:BUNDLE ").length>0;this.usingBundle=d;var p=r.matchPrefix(f,"a=ice-options:")[0];switch(this.canTrickleIceCandidates=!!p&&p.substr(14).split(" ").indexOf("trickle")>=0,u.forEach((function(o,a){var u=r.splitLines(o),p=r.getKind(o),h=r.isRejected(o),v=u[0].substr(2).split(" ")[2],m=r.getDirection(o,f),g=r.parseMsid(o),y=r.getMid(o)||r.generateIdentifier();if("application"!==p||"DTLS/SCTP"!==v){var _,C,b,w,S,T,R,E,P,k,x,O=r.parseRtpParameters(o);h||(k=r.getIceParameters(o,f),(x=r.getDtlsParameters(o,f)).role="client"),R=r.parseRtpEncodingParameters(o);var I=r.parseRtcpParameters(o),j=r.matchPrefix(o,"a=end-of-candidates",f).length>0,D=r.matchPrefix(o,"a=candidate:").map((function(e){return r.parseCandidate(e)})).filter((function(e){return"1"===e.component||1===e.component}));("offer"===n.type||"answer"===n.type)&&!h&&d&&a>0&&i.transceivers[a]&&(i._disposeIceAndDtlsTransports(a),i.transceivers[a].iceGatherer=i.transceivers[0].iceGatherer,i.transceivers[a].iceTransport=i.transceivers[0].iceTransport,i.transceivers[a].dtlsTransport=i.transceivers[0].dtlsTransport,i.transceivers[a].rtpSender&&i.transceivers[a].rtpSender.setTransport(i.transceivers[0].dtlsTransport),i.transceivers[a].rtpReceiver&&i.transceivers[a].rtpReceiver.setTransport(i.transceivers[0].dtlsTransport)),"offer"!==n.type||h?"answer"!==n.type||h||(C=(_=i.transceivers[a]).iceGatherer,b=_.iceTransport,w=_.dtlsTransport,S=_.rtpReceiver,T=_.sendEncodingParameters,E=_.localCapabilities,i.transceivers[a].recvEncodingParameters=R,i.transceivers[a].remoteCapabilities=O,i.transceivers[a].rtcpParameters=I,d&&0!==a||((l||j)&&D.length&&b.setRemoteCandidates(D),b.start(C,k,"controlling"),w.start(x)),i._transceive(_,"sendrecv"===m||"recvonly"===m,"sendrecv"===m||"sendonly"===m),!S||"sendrecv"!==m&&"sendonly"!==m?delete _.rtpReceiver:(P=S.track,g?(s[g.stream]||(s[g.stream]=new e.MediaStream),s[g.stream].addTrack(P),c.push([P,S,s[g.stream]])):(s.default||(s.default=new e.MediaStream),s.default.addTrack(P),c.push([P,S,s.default])))):((_=i.transceivers[a]||i._createTransceiver(p)).mid=y,_.iceGatherer||(_.iceGatherer=d&&a>0?i.transceivers[0].iceGatherer:i._createIceGatherer(y,a)),!j||!D.length||d&&0!==a||_.iceTransport.setRemoteCandidates(D),E=e.RTCRtpReceiver.getCapabilities(p),t<15019&&(E.codecs=E.codecs.filter((function(e){return"rtx"!==e.name}))),T=[{ssrc:1001*(2*a+2)}],"sendrecv"!==m&&"sendonly"!==m||(P=(S=new e.RTCRtpReceiver(_.dtlsTransport,p)).track,g?(s[g.stream]||(s[g.stream]=new e.MediaStream,Object.defineProperty(s[g.stream],"id",{get:function(){return g.stream}})),Object.defineProperty(P,"id",{get:function(){return g.track}}),s[g.stream].addTrack(P),c.push([P,S,s[g.stream]])):(s.default||(s.default=new e.MediaStream),s.default.addTrack(P),c.push([P,S,s.default]))),_.localCapabilities=E,_.remoteCapabilities=O,_.rtpReceiver=S,_.rtcpParameters=I,_.sendEncodingParameters=T,_.recvEncodingParameters=R,i._transceive(i.transceivers[a],!1,"sendrecv"===m||"sendonly"===m))}else i.transceivers[a]={mid:y,isDatachannel:!0}})),this.remoteDescription={type:n.type,sdp:n.sdp},n.type){case"offer":this._updateSignalingState("have-remote-offer");break;case"answer":this._updateSignalingState("stable");break;default:throw new TypeError('unsupported type "'+n.type+'"')}return Object.keys(s).forEach((function(t){var n=s[t];if(n.getTracks().length){i.remoteStreams.push(n);var r=new Event("addstream");r.stream=n,i.dispatchEvent(r),null!==i.onaddstream&&e.setTimeout((function(){i.onaddstream(r)}),0),c.forEach((function(t){var r=t[0],o=t[1];if(n.id===t[2].id){var a=new Event("track");a.track=r,a.receiver=o,a.streams=[n],i.dispatchEvent(a),null!==i.ontrack&&e.setTimeout((function(){i.ontrack(a)}),0)}}))}})),e.setTimeout((function(){i&&i.transceivers&&i.transceivers.forEach((function(e){e.iceTransport&&"new"===e.iceTransport.state&&e.iceTransport.getRemoteCandidates().length>0&&(console.warn("Timeout for addRemoteCandidate. Consider sending an end-of-candidates notification"),e.iceTransport.addRemoteCandidate({}))}))}),4e3),arguments.length>1&&"function"==typeof arguments[1]&&e.setTimeout(arguments[1],0),Promise.resolve()},n.prototype.close=function(){this.transceivers.forEach((function(e){e.iceTransport&&e.iceTransport.stop(),e.dtlsTransport&&e.dtlsTransport.stop(),e.rtpSender&&e.rtpSender.stop(),e.rtpReceiver&&e.rtpReceiver.stop()})),this._updateSignalingState("closed")},n.prototype._updateSignalingState=function(e){this.signalingState=e;var t=new Event("signalingstatechange");this.dispatchEvent(t),null!==this.onsignalingstatechange&&this.onsignalingstatechange(t)},n.prototype._maybeFireNegotiationNeeded=function(){var t=this;"stable"===this.signalingState&&!0!==this.needNegotiation&&(this.needNegotiation=!0,e.setTimeout((function(){if(!1!==t.needNegotiation){t.needNegotiation=!1;var e=new Event("negotiationneeded");t.dispatchEvent(e),null!==t.onnegotiationneeded&&t.onnegotiationneeded(e)}}),0))},n.prototype._updateConnectionState=function(){var e,t={new:0,closed:0,connecting:0,checking:0,connected:0,completed:0,disconnected:0,failed:0};if(this.transceivers.forEach((function(e){t[e.iceTransport.state]++,t[e.dtlsTransport.state]++})),t.connected+=t.completed,e="new",t.failed>0?e="failed":t.connecting>0||t.checking>0?e="connecting":t.disconnected>0?e="disconnected":t.new>0?e="new":(t.connected>0||t.completed>0)&&(e="connected"),e!==this.iceConnectionState){this.iceConnectionState=e;var n=new Event("iceconnectionstatechange");this.dispatchEvent(n),null!==this.oniceconnectionstatechange&&this.oniceconnectionstatechange(n)}},n.prototype.createOffer=function(){var n,i=this;if(this._pendingOffer)throw new Error("createOffer called while there is a pending offer.");1===arguments.length&&"function"!=typeof arguments[0]?n=arguments[0]:3===arguments.length&&(n=arguments[2]);var o=this.transceivers.filter((function(e){return"audio"===e.kind})).length,a=this.transceivers.filter((function(e){return"video"===e.kind})).length;if(n){if(n.mandatory||n.optional)throw new TypeError("Legacy mandatory/optional constraints not supported.");void 0!==n.offerToReceiveAudio&&(o=!0===n.offerToReceiveAudio?1:!1===n.offerToReceiveAudio?0:n.offerToReceiveAudio),void 0!==n.offerToReceiveVideo&&(a=!0===n.offerToReceiveVideo?1:!1===n.offerToReceiveVideo?0:n.offerToReceiveVideo)}for(this.transceivers.forEach((function(e){"audio"===e.kind?--o<0&&(e.wantReceive=!1):"video"===e.kind&&--a<0&&(e.wantReceive=!1)}));o>0||a>0;)o>0&&(this._createTransceiver("audio"),o--),a>0&&(this._createTransceiver("video"),a--);var s=function(e){var t=e.filter((function(e){return"audio"===e.kind})),n=e.filter((function(e){return"video"===e.kind}));for(e=[];t.length||n.length;)t.length&&e.push(t.shift()),n.length&&e.push(n.shift());return e}(this.transceivers),c=r.writeSessionBoilerplate(this._sdpSessionId);s.forEach((function(n,o){var a=n.track,c=n.kind,u=r.generateIdentifier();n.mid=u,n.iceGatherer||(n.iceGatherer=i.usingBundle&&o>0?s[0].iceGatherer:i._createIceGatherer(u,o));var f=e.RTCRtpSender.getCapabilities(c);t<15019&&(f.codecs=f.codecs.filter((function(e){return"rtx"!==e.name}))),f.codecs.forEach((function(e){"H264"===e.name&&void 0===e.parameters["level-asymmetry-allowed"]&&(e.parameters["level-asymmetry-allowed"]="1")}));var l=[{ssrc:1001*(2*o+1)}];a&&t>=15019&&"video"===c&&(l[0].rtx={ssrc:1001*(2*o+1)+1}),n.wantReceive&&(n.rtpReceiver=new e.RTCRtpReceiver(n.dtlsTransport,c)),n.localCapabilities=f,n.sendEncodingParameters=l})),"max-compat"!==this._config.bundlePolicy&&(c+="a=group:BUNDLE "+s.map((function(e){return e.mid})).join(" ")+"\r\n"),c+="a=ice-options:trickle\r\n",s.forEach((function(e,t){c+=r.writeMediaSection(e,e.localCapabilities,"offer",e.stream),c+="a=rtcp-rsize\r\n"})),this._pendingOffer=s;var u=new e.RTCSessionDescription({type:"offer",sdp:c});return arguments.length&&"function"==typeof arguments[0]&&e.setTimeout(arguments[0],0,u),Promise.resolve(u)},n.prototype.createAnswer=function(){var n=r.writeSessionBoilerplate(this._sdpSessionId);this.usingBundle&&(n+="a=group:BUNDLE "+this.transceivers.map((function(e){return e.mid})).join(" ")+"\r\n"),this.transceivers.forEach((function(e,o){if(e.isDatachannel)n+="m=application 0 DTLS/SCTP 5000\r\nc=IN IP4 0.0.0.0\r\na=mid:"+e.mid+"\r\n";else{var a;e.stream&&("audio"===e.kind?a=e.stream.getAudioTracks()[0]:"video"===e.kind&&(a=e.stream.getVideoTracks()[0]),a&&t>=15019&&"video"===e.kind&&(e.sendEncodingParameters[0].rtx={ssrc:1001*(2*o+2)+1}));var s=i(e.localCapabilities,e.remoteCapabilities);!s.codecs.filter((function(e){return"rtx"===e.name.toLowerCase()})).length&&e.sendEncodingParameters[0].rtx&&delete e.sendEncodingParameters[0].rtx,n+=r.writeMediaSection(e,s,"answer",e.stream),e.rtcpParameters&&e.rtcpParameters.reducedSize&&(n+="a=rtcp-rsize\r\n")}}));var o=new e.RTCSessionDescription({type:"answer",sdp:n});return arguments.length&&"function"==typeof arguments[0]&&e.setTimeout(arguments[0],0,o),Promise.resolve(o)},n.prototype.addIceCandidate=function(t){if(t){var n=t.sdpMLineIndex;if(t.sdpMid)for(var i=0;i<this.transceivers.length;i++)if(this.transceivers[i].mid===t.sdpMid){n=i;break}var o=this.transceivers[n];if(o){var a=Object.keys(t.candidate).length>0?r.parseCandidate(t.candidate):{};if("tcp"===a.protocol&&(0===a.port||9===a.port))return Promise.resolve();if(a.component&&"1"!==a.component&&1!==a.component)return Promise.resolve();o.iceTransport.addRemoteCandidate(a);var s=r.splitSections(this.remoteDescription.sdp);s[n+1]+=(a.type?t.candidate.trim():"a=end-of-candidates")+"\r\n",this.remoteDescription.sdp=s.join("")}}else for(var c=0;c<this.transceivers.length;c++)if(this.transceivers[c].iceTransport.addRemoteCandidate({}),this.usingBundle)return Promise.resolve();return arguments.length>1&&"function"==typeof arguments[1]&&e.setTimeout(arguments[1],0),Promise.resolve()},n.prototype.getStats=function(){var t=[];this.transceivers.forEach((function(e){["rtpSender","rtpReceiver","iceGatherer","iceTransport","dtlsTransport"].forEach((function(n){e[n]&&t.push(e[n].getStats())}))}));var n=arguments.length>1&&"function"==typeof arguments[1]&&arguments[1];return new Promise((function(r){var i=new Map;Promise.all(t).then((function(t){t.forEach((function(e){Object.keys(e).forEach((function(t){var n;e[t].type={inboundrtp:"inbound-rtp",outboundrtp:"outbound-rtp",candidatepair:"candidate-pair",localcandidate:"local-candidate",remotecandidate:"remote-candidate"}[(n=e[t]).type]||n.type,i.set(t,e[t])}))})),n&&e.setTimeout(n,0,i),r(i)}))}))},n}},542:(e,t,n)=>{"use strict";var r=n(634),i={shimOnTrack:function(e){"object"==typeof e&&e.RTCPeerConnection&&!("ontrack"in e.RTCPeerConnection.prototype)&&Object.defineProperty(e.RTCPeerConnection.prototype,"ontrack",{get:function(){return this._ontrack},set:function(e){this._ontrack&&(this.removeEventListener("track",this._ontrack),this.removeEventListener("addstream",this._ontrackpoly)),this.addEventListener("track",this._ontrack=e),this.addEventListener("addstream",this._ontrackpoly=function(e){e.stream.getTracks().forEach(function(t){var n=new Event("track");n.track=t,n.receiver={track:t},n.streams=[e.stream],this.dispatchEvent(n)}.bind(this))}.bind(this))}})},shimSourceObject:function(e){"object"==typeof e&&e.HTMLMediaElement&&!("srcObject"in e.HTMLMediaElement.prototype)&&Object.defineProperty(e.HTMLMediaElement.prototype,"srcObject",{get:function(){return this.mozSrcObject},set:function(e){this.mozSrcObject=e}})},shimPeerConnection:function(e){var t=r.detectBrowser(e);if("object"==typeof e&&(e.RTCPeerConnection||e.mozRTCPeerConnection)){e.RTCPeerConnection||(e.RTCPeerConnection=function(n,r){if(t.version<38&&n&&n.iceServers){for(var i=[],o=0;o<n.iceServers.length;o++){var a=n.iceServers[o];if(a.hasOwnProperty("urls"))for(var s=0;s<a.urls.length;s++){var c={url:a.urls[s]};0===a.urls[s].indexOf("turn")&&(c.username=a.username,c.credential=a.credential),i.push(c)}else i.push(n.iceServers[o])}n.iceServers=i}return new e.mozRTCPeerConnection(n,r)},e.RTCPeerConnection.prototype=e.mozRTCPeerConnection.prototype,e.mozRTCPeerConnection.generateCertificate&&Object.defineProperty(e.RTCPeerConnection,"generateCertificate",{get:function(){return e.mozRTCPeerConnection.generateCertificate}}),e.RTCSessionDescription=e.mozRTCSessionDescription,e.RTCIceCandidate=e.mozRTCIceCandidate),["setLocalDescription","setRemoteDescription","addIceCandidate"].forEach((function(t){var n=e.RTCPeerConnection.prototype[t];e.RTCPeerConnection.prototype[t]=function(){return arguments[0]=new("addIceCandidate"===t?e.RTCIceCandidate:e.RTCSessionDescription)(arguments[0]),n.apply(this,arguments)}}));var n=e.RTCPeerConnection.prototype.addIceCandidate;e.RTCPeerConnection.prototype.addIceCandidate=function(){return arguments[0]?n.apply(this,arguments):(arguments[1]&&arguments[1].apply(null),Promise.resolve())};var i={inboundrtp:"inbound-rtp",outboundrtp:"outbound-rtp",candidatepair:"candidate-pair",localcandidate:"local-candidate",remotecandidate:"remote-candidate"},o=e.RTCPeerConnection.prototype.getStats;e.RTCPeerConnection.prototype.getStats=function(e,n,r){return o.apply(this,[e||null]).then((function(e){if(t.version<48&&(e=function(e){var t=new Map;return Object.keys(e).forEach((function(n){t.set(n,e[n]),t[n]=e[n]})),t}(e)),t.version<53&&!n)try{e.forEach((function(e){e.type=i[e.type]||e.type}))}catch(t){if("TypeError"!==t.name)throw t;e.forEach((function(t,n){e.set(n,Object.assign({},t,{type:i[t.type]||t.type}))}))}return e})).then(n,r)}}}};e.exports={shimOnTrack:i.shimOnTrack,shimSourceObject:i.shimSourceObject,shimPeerConnection:i.shimPeerConnection,shimGetUserMedia:n(176)}},176:(e,t,n)=>{"use strict";var r=n(634),i=r.log;e.exports=function(e){var t=r.detectBrowser(e),n=e&&e.navigator,o=e&&e.MediaStreamTrack,a=function(e){return{name:{InternalError:"NotReadableError",NotSupportedError:"TypeError",PermissionDeniedError:"NotAllowedError",SecurityError:"NotAllowedError"}[e.name]||e.name,message:{"The operation is insecure.":"The request is not allowed by the user agent or the platform in the current context."}[e.message]||e.message,constraint:e.constraint,toString:function(){return this.name+(this.message&&": ")+this.message}}},s=function(e,r,o){var s=function(e){if("object"!=typeof e||e.require)return e;var t=[];return Object.keys(e).forEach((function(n){if("require"!==n&&"advanced"!==n&&"mediaSource"!==n){var r=e[n]="object"==typeof e[n]?e[n]:{ideal:e[n]};if(void 0===r.min&&void 0===r.max&&void 0===r.exact||t.push(n),void 0!==r.exact&&("number"==typeof r.exact?r.min=r.max=r.exact:e[n]=r.exact,delete r.exact),void 0!==r.ideal){e.advanced=e.advanced||[];var i={};"number"==typeof r.ideal?i[n]={min:r.ideal,max:r.ideal}:i[n]=r.ideal,e.advanced.push(i),delete r.ideal,Object.keys(r).length||delete e[n]}}})),t.length&&(e.require=t),e};return e=JSON.parse(JSON.stringify(e)),t.version<38&&(i("spec: "+JSON.stringify(e)),e.audio&&(e.audio=s(e.audio)),e.video&&(e.video=s(e.video)),i("ff37: "+JSON.stringify(e))),n.mozGetUserMedia(e,r,(function(e){o(a(e))}))};if(n.mediaDevices||(n.mediaDevices={getUserMedia:function(e){return new Promise((function(t,n){s(e,t,n)}))},addEventListener:function(){},removeEventListener:function(){}}),n.mediaDevices.enumerateDevices=n.mediaDevices.enumerateDevices||function(){return new Promise((function(e){e([{kind:"audioinput",deviceId:"default",label:"",groupId:""},{kind:"videoinput",deviceId:"default",label:"",groupId:""}])}))},t.version<41){var c=n.mediaDevices.enumerateDevices.bind(n.mediaDevices);n.mediaDevices.enumerateDevices=function(){return c().then(void 0,(function(e){if("NotFoundError"===e.name)return[];throw e}))}}if(t.version<49){var u=n.mediaDevices.getUserMedia.bind(n.mediaDevices);n.mediaDevices.getUserMedia=function(e){return u(e).then((function(t){if(e.audio&&!t.getAudioTracks().length||e.video&&!t.getVideoTracks().length)throw t.getTracks().forEach((function(e){e.stop()})),new DOMException("The object can not be found here.","NotFoundError");return t}),(function(e){return Promise.reject(a(e))}))}}if(!(t.version>55&&"autoGainControl"in n.mediaDevices.getSupportedConstraints())){var f=function(e,t,n){t in e&&!(n in e)&&(e[n]=e[t],delete e[t])},l=n.mediaDevices.getUserMedia.bind(n.mediaDevices);if(n.mediaDevices.getUserMedia=function(e){return"object"==typeof e&&"object"==typeof e.audio&&(e=JSON.parse(JSON.stringify(e)),f(e.audio,"autoGainControl","mozAutoGainControl"),f(e.audio,"noiseSuppression","mozNoiseSuppression")),l(e)},o&&o.prototype.getSettings){var d=o.prototype.getSettings;o.prototype.getSettings=function(){var e=d.apply(this,arguments);return f(e,"mozAutoGainControl","autoGainControl"),f(e,"mozNoiseSuppression","noiseSuppression"),e}}if(o&&o.prototype.applyConstraints){var p=o.prototype.applyConstraints;o.prototype.applyConstraints=function(e){return"audio"===this.kind&&"object"==typeof e&&(e=JSON.parse(JSON.stringify(e)),f(e,"autoGainControl","mozAutoGainControl"),f(e,"noiseSuppression","mozNoiseSuppression")),p.apply(this,[e])}}}n.getUserMedia=function(e,i,o){if(t.version<44)return s(e,i,o);r.deprecated("navigator.getUserMedia","navigator.mediaDevices.getUserMedia"),n.mediaDevices.getUserMedia(e).then(i,o)}}},824:(e,t,n)=>{"use strict";var r=n(634),i={shimLocalStreamsAPI:function(e){if("object"==typeof e&&e.RTCPeerConnection){if("getLocalStreams"in e.RTCPeerConnection.prototype||(e.RTCPeerConnection.prototype.getLocalStreams=function(){return this._localStreams||(this._localStreams=[]),this._localStreams}),"getStreamById"in e.RTCPeerConnection.prototype||(e.RTCPeerConnection.prototype.getStreamById=function(e){var t=null;return this._localStreams&&this._localStreams.forEach((function(n){n.id===e&&(t=n)})),this._remoteStreams&&this._remoteStreams.forEach((function(n){n.id===e&&(t=n)})),t}),!("addStream"in e.RTCPeerConnection.prototype)){var t=e.RTCPeerConnection.prototype.addTrack;e.RTCPeerConnection.prototype.addStream=function(e){this._localStreams||(this._localStreams=[]),-1===this._localStreams.indexOf(e)&&this._localStreams.push(e);var n=this;e.getTracks().forEach((function(r){t.call(n,r,e)}))},e.RTCPeerConnection.prototype.addTrack=function(e,n){n&&(this._localStreams?-1===this._localStreams.indexOf(n)&&this._localStreams.push(n):this._localStreams=[n]),t.call(this,e,n)}}"removeStream"in e.RTCPeerConnection.prototype||(e.RTCPeerConnection.prototype.removeStream=function(e){this._localStreams||(this._localStreams=[]);var t=this._localStreams.indexOf(e);if(-1!==t){this._localStreams.splice(t,1);var n=this,r=e.getTracks();this.getSenders().forEach((function(e){-1!==r.indexOf(e.track)&&n.removeTrack(e)}))}})}},shimRemoteStreamsAPI:function(e){"object"==typeof e&&e.RTCPeerConnection&&("getRemoteStreams"in e.RTCPeerConnection.prototype||(e.RTCPeerConnection.prototype.getRemoteStreams=function(){return this._remoteStreams?this._remoteStreams:[]}),"onaddstream"in e.RTCPeerConnection.prototype||Object.defineProperty(e.RTCPeerConnection.prototype,"onaddstream",{get:function(){return this._onaddstream},set:function(e){this._onaddstream&&(this.removeEventListener("addstream",this._onaddstream),this.removeEventListener("track",this._onaddstreampoly)),this.addEventListener("addstream",this._onaddstream=e),this.addEventListener("track",this._onaddstreampoly=function(e){var t=e.streams[0];if(this._remoteStreams||(this._remoteStreams=[]),!(this._remoteStreams.indexOf(t)>=0)){this._remoteStreams.push(t);var n=new Event("addstream");n.stream=e.streams[0],this.dispatchEvent(n)}}.bind(this))}}))},shimCallbacksAPI:function(e){if("object"==typeof e&&e.RTCPeerConnection){var t=e.RTCPeerConnection.prototype,n=t.createOffer,r=t.createAnswer,i=t.setLocalDescription,o=t.setRemoteDescription,a=t.addIceCandidate;t.createOffer=function(e,t){var r=arguments.length>=2?arguments[2]:arguments[0],i=n.apply(this,[r]);return t?(i.then(e,t),Promise.resolve()):i},t.createAnswer=function(e,t){var n=arguments.length>=2?arguments[2]:arguments[0],i=r.apply(this,[n]);return t?(i.then(e,t),Promise.resolve()):i};var s=function(e,t,n){var r=i.apply(this,[e]);return n?(r.then(t,n),Promise.resolve()):r};t.setLocalDescription=s,s=function(e,t,n){var r=o.apply(this,[e]);return n?(r.then(t,n),Promise.resolve()):r},t.setRemoteDescription=s,s=function(e,t,n){var r=a.apply(this,[e]);return n?(r.then(t,n),Promise.resolve()):r},t.addIceCandidate=s}},shimGetUserMedia:function(e){var t=e&&e.navigator;t.getUserMedia||(t.webkitGetUserMedia?t.getUserMedia=t.webkitGetUserMedia.bind(t):t.mediaDevices&&t.mediaDevices.getUserMedia&&(t.getUserMedia=function(e,n,r){t.mediaDevices.getUserMedia(e).then(n,r)}.bind(t)))},shimRTCIceServerUrls:function(e){var t=e.RTCPeerConnection;e.RTCPeerConnection=function(e,n){if(e&&e.iceServers){for(var i=[],o=0;o<e.iceServers.length;o++){var a=e.iceServers[o];!a.hasOwnProperty("urls")&&a.hasOwnProperty("url")?(r.deprecated("RTCIceServer.url","RTCIceServer.urls"),(a=JSON.parse(JSON.stringify(a))).urls=a.url,delete a.url,i.push(a)):i.push(e.iceServers[o])}e.iceServers=i}return new t(e,n)},e.RTCPeerConnection.prototype=t.prototype,Object.defineProperty(e.RTCPeerConnection,"generateCertificate",{get:function(){return t.generateCertificate}})}};e.exports={shimCallbacksAPI:i.shimCallbacksAPI,shimLocalStreamsAPI:i.shimLocalStreamsAPI,shimRemoteStreamsAPI:i.shimRemoteStreamsAPI,shimGetUserMedia:i.shimGetUserMedia,shimRTCIceServerUrls:i.shimRTCIceServerUrls}},634:e=>{"use strict";var t=!0,n=!0,r={disableLog:function(e){return"boolean"!=typeof e?new Error("Argument type: "+typeof e+". Please use a boolean."):(t=e,e?"adapter.js logging disabled":"adapter.js logging enabled")},disableWarnings:function(e){return"boolean"!=typeof e?new Error("Argument type: "+typeof e+". Please use a boolean."):(n=!e,"adapter.js deprecation warnings "+(e?"disabled":"enabled"))},log:function(){if("object"==typeof window){if(t)return;"undefined"!=typeof console&&"function"==typeof console.log&&console.log.apply(console,arguments)}},deprecated:function(e,t){n&&console.warn(e+" is deprecated, please use "+t+" instead.")},extractVersion:function(e,t,n){var r=e.match(t);return r&&r.length>=n&&parseInt(r[n],10)},detectBrowser:function(e){var t=e&&e.navigator,n={browser:null,version:null};if(void 0===e||!e.navigator)return n.browser="Not a browser.",n;if(t.mozGetUserMedia)n.browser="firefox",n.version=this.extractVersion(t.userAgent,/Firefox\/(\d+)\./,1);else if(t.webkitGetUserMedia)if(e.webkitRTCPeerConnection)n.browser="chrome",n.version=this.extractVersion(t.userAgent,/Chrom(e|ium)\/(\d+)\./,2);else{if(!t.userAgent.match(/Version\/(\d+).(\d+)/))return n.browser="Unsupported webkit-based browser with GUM support but no WebRTC support.",n;n.browser="safari",n.version=this.extractVersion(t.userAgent,/AppleWebKit\/(\d+)\./,1)}else if(t.mediaDevices&&t.userAgent.match(/Edge\/(\d+).(\d+)$/))n.browser="edge",n.version=this.extractVersion(t.userAgent,/Edge\/(\d+).(\d+)$/,2);else{if(!t.mediaDevices||!t.userAgent.match(/AppleWebKit\/(\d+)\./))return n.browser="Not a supported browser.",n;n.browser="safari",n.version=this.extractVersion(t.userAgent,/AppleWebKit\/(\d+)\./,1)}return n},shimCreateObjectURL:function(e){var t=e&&e.URL;if("object"==typeof e&&e.HTMLMediaElement&&"srcObject"in e.HTMLMediaElement.prototype){var n=t.createObjectURL.bind(t),i=t.revokeObjectURL.bind(t),o=new Map,a=0;t.createObjectURL=function(e){if("getTracks"in e){var t="polyblob:"+ ++a;return o.set(t,e),r.deprecated("URL.createObjectURL(stream)","elem.srcObject = stream"),t}return n(e)},t.revokeObjectURL=function(e){i(e),o.delete(e)};var s=Object.getOwnPropertyDescriptor(e.HTMLMediaElement.prototype,"src");Object.defineProperty(e.HTMLMediaElement.prototype,"src",{get:function(){return s.get.apply(this)},set:function(e){return this.srcObject=o.get(e)||null,s.set.apply(this,[e])}});var c=e.HTMLMediaElement.prototype.setAttribute;e.HTMLMediaElement.prototype.setAttribute=function(){return 2===arguments.length&&"src"===(""+arguments[0]).toLowerCase()&&(this.srcObject=o.get(arguments[1])||null),c.apply(this,arguments)}}}};e.exports={log:r.log,deprecated:r.deprecated,disableLog:r.disableLog,disableWarnings:r.disableWarnings,extractVersion:r.extractVersion,shimCreateObjectURL:r.shimCreateObjectURL,detectBrowser:r.detectBrowser.bind(r)}},55:e=>{"use strict";e.exports=t},308:t=>{"use strict";t.exports=e},330:e=>{"use strict";e.exports={version:"0.19.0"}}},r={};function i(e){var t=r[e];if(void 0!==t)return t.exports;var o=r[e]={id:e,loaded:!1,exports:{}};return n[e].call(o.exports,o,o.exports,i),o.loaded=!0,o.exports}return i.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),i.nmd=e=>(e.paths=[],e.children||(e.children=[]),e),i.p="",i(925),i(509)})()));