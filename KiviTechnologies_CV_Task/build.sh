#!/bin/bash

# Build script for Kivi Technologies CV Task

echo "=== Kivi Technologies CV Task Build Script ==="

# Create build directory
if [ ! -d "build" ]; then
    echo "Creating build directory..."
    mkdir build
fi

# Navigate to build directory
cd build

# Run CMake
echo "Running CMake..."
cmake ..

# Check if CMake succeeded
if [ $? -ne 0 ]; then
    echo "CMake failed! Please check your OpenCV installation."
    echo "On Ubuntu/Debian: sudo apt-get install libopencv-dev"
    echo "On macOS: brew install opencv"
    exit 1
fi

# Build the project
echo "Building project..."
make -j$(nproc)

# Check if build succeeded
if [ $? -eq 0 ]; then
    echo ""
    echo "=== Build Successful! ==="
    echo "Executable created: build/bin/KiviTechnologies_CV_Task"
    echo ""
    echo "To run the program:"
    echo "  cd build"
    echo "  ./bin/KiviTechnologies_CV_Task"
    echo ""
    echo "Or use the run script:"
    echo "  ./run.sh"
else
    echo "Build failed! Please check the error messages above."
    exit 1
fi
