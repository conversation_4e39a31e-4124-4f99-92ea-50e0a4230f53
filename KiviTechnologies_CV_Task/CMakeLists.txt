cmake_minimum_required(VERSION 3.10)
project(KiviTechnologies_CV_Task)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Find required packages
find_package(OpenCV REQUIRED)

# Include directories
include_directories(${OpenCV_INCLUDE_DIRS})
include_directories(${CMAKE_SOURCE_DIR}/include)

# Create include directory for header files
file(MAKE_DIRECTORY ${CMAKE_SOURCE_DIR}/include)

# Add executable
add_executable(${PROJECT_NAME} 
    src/main.cpp
    src/utils.cpp
    src/double_three_step.cpp
    src/bilateral_filter_3d.cpp
    src/correspondence_matching.cpp
)

# Link libraries
target_link_libraries(${PROJECT_NAME} ${OpenCV_LIBS})

# Set output directory
set_target_properties(${PROJECT_NAME} PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
)

# Create necessary directories
file(MAKE_DIRECTORY ${CMAKE_SOURCE_DIR}/src)
file(MAKE_DIRECTORY ${CMAKE_BINARY_DIR}/bin)
file(MAKE_DIRECTORY ${CMAKE_BINARY_DIR}/output)

# Copy data files to build directory for easy access
file(COPY ${CMAKE_SOURCE_DIR}/Tasks/ DESTINATION ${CMAKE_BINARY_DIR}/data/)

# Compiler-specific options
if(MSVC)
    target_compile_options(${PROJECT_NAME} PRIVATE /W4)
else()
    target_compile_options(${PROJECT_NAME} PRIVATE -Wall -Wextra -Wpedantic)
endif()

# Debug/Release configurations
set(CMAKE_CXX_FLAGS_DEBUG "-g -O0")
set(CMAKE_CXX_FLAGS_RELEASE "-O3 -DNDEBUG")
