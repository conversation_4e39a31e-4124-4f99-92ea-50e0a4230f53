#!/bin/bash

# Run script for Kivi Technologies CV Task

echo "=== Kivi Technologies CV Task Runner ==="

# Check if executable exists
if [ ! -f "build/bin/KiviTechnologies_CV_Task" ]; then
    echo "Executable not found! Please build the project first:"
    echo "  ./build.sh"
    exit 1
fi

# Create output directory if it doesn't exist
mkdir -p output

# Navigate to build directory and run
cd build
echo "Starting the application..."
echo ""
./bin/KiviTechnologies_CV_Task
